'use client';

import { useEffect, useRef, useState } from 'react';
import { useLocation } from 'blade/hooks';
import { useUnifiedSession } from '../../lib/auth-client';

interface RevalidationEvent {
  timestamp: number;
  type: 'page_load' | 'location_change' | 'session_change' | 'unknown';
  details: any;
}

export function RevalidationDebugger() {
  const location = useLocation();
  const { session, loading } = useUnifiedSession();
  const [events, setEvents] = useState<RevalidationEvent[]>([]);
  const [isVisible, setIsVisible] = useState(false);
  const lastLocationRef = useRef(location.pathname);
  const lastSessionRef = useRef(session?.user?.id);
  const renderCountRef = useRef(0);

  // Track component renders
  renderCountRef.current += 1;

  // Track location changes
  useEffect(() => {
    if (lastLocationRef.current !== location.pathname) {
      setEvents(prev => [...prev.slice(-9), {
        timestamp: Date.now(),
        type: 'location_change',
        details: {
          from: lastLocationRef.current,
          to: location.pathname,
          renderCount: renderCountRef.current
        }
      }]);
      lastLocationRef.current = location.pathname;
    }
  }, [location.pathname]);

  // Track session changes
  useEffect(() => {
    if (lastSessionRef.current !== session?.user?.id) {
      setEvents(prev => [...prev.slice(-9), {
        timestamp: Date.now(),
        type: 'session_change',
        details: {
          from: lastSessionRef.current,
          to: session?.user?.id,
          loading,
          renderCount: renderCountRef.current
        }
      }]);
      lastSessionRef.current = session?.user?.id;
    }
  }, [session?.user?.id, loading]);

  // Track page loads
  useEffect(() => {
    setEvents(prev => [...prev.slice(-9), {
      timestamp: Date.now(),
      type: 'page_load',
      details: {
        pathname: location.pathname,
        renderCount: renderCountRef.current
      }
    }]);
  }, []); // Only on mount

  // Monitor for excessive re-renders
  useEffect(() => {
    const interval = setInterval(() => {
      if (renderCountRef.current > 10) {
        console.warn(`🚨 RevalidationDebugger: ${renderCountRef.current} renders detected in 5 seconds`);
        setEvents(prev => [...prev.slice(-9), {
          timestamp: Date.now(),
          type: 'unknown',
          details: {
            excessiveRenders: renderCountRef.current,
            pathname: location.pathname
          }
        }]);
        renderCountRef.current = 0; // Reset counter
      }
    }, 5000);

    return () => clearInterval(interval);
  }, [location.pathname]);

  if (!isVisible) {
    return (
      <button
        onClick={() => setIsVisible(true)}
        className="fixed bottom-4 right-4 bg-red-500 text-white px-3 py-1 rounded text-xs z-50"
      >
        Debug Revalidation
      </button>
    );
  }

  return (
    <div className="fixed bottom-4 right-4 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-lg p-4 max-w-md max-h-96 overflow-y-auto z-50 shadow-lg">
      <div className="flex justify-between items-center mb-2">
        <h3 className="text-sm font-bold">Revalidation Events</h3>
        <button
          onClick={() => setIsVisible(false)}
          className="text-gray-500 hover:text-gray-700 text-xs"
        >
          ✕
        </button>
      </div>
      
      <div className="text-xs mb-2">
        <div>Renders: {renderCountRef.current}</div>
        <div>Events: {events.length}</div>
        <div>Current: {location.pathname}</div>
      </div>

      <div className="space-y-1">
        {events.slice(-10).map((event, index) => {
          const time = new Date(event.timestamp).toLocaleTimeString();
          const typeColor = {
            page_load: 'text-blue-600',
            location_change: 'text-green-600',
            session_change: 'text-orange-600',
            unknown: 'text-red-600'
          }[event.type];

          return (
            <div key={index} className={`text-xs ${typeColor}`}>
              <div className="font-mono">{time} - {event.type}</div>
              <div className="text-gray-600 dark:text-gray-400 ml-2">
                {JSON.stringify(event.details, null, 0).slice(0, 100)}...
              </div>
            </div>
          );
        })}
      </div>

      <button
        onClick={() => setEvents([])}
        className="mt-2 bg-gray-200 dark:bg-gray-700 px-2 py-1 rounded text-xs"
      >
        Clear Events
      </button>
    </div>
  );
}