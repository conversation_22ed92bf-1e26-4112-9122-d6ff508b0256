// triggers/school.ts
import type { <PERSON>d<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ger, GetTrigger, RemoveTrigger } from 'blade/types';

// During triggers - required by Blade for write operations
export const during = {
  add: (...args: any[]) => {
    const [query, _multiple, _options] = args;
    const typedQuery = query as any;

    // Ensure query.with exists
    if (!typedQuery.with) {
      return typedQuery;
    }

    // Handle both single object and array cases
    const processSchoolData = (schoolData: any) => {
      console.log('School during.add trigger - processing data:', schoolData);

      // Set default values
      schoolData.isActive = schoolData.isActive !== false; // Default to true
      schoolData.studentCount = schoolData.studentCount || 0;
      schoolData.teacherCount = schoolData.teacherCount || 0;
      schoolData.createdAt = schoolData.createdAt || new Date();
      schoolData.updatedAt = schoolData.updatedAt || new Date();

      console.log('School during.add trigger - processed data:', schoolData);
      return schoolData;
    };

    // Handle array of schools
    if (Array.isArray(typedQuery.with)) {
      typedQuery.with = typedQuery.with.map(processSchoolData);
    } else {
      // Handle single school
      typedQuery.with = processSchoolData(typedQuery.with);
    }

    return typedQuery;
  },

  set: (...args: any[]) => {
    const [query, _multiple, _options] = args;
    const typedQuery = query as any;

    // Ensure query.to exists
    if (!typedQuery.to) {
      return typedQuery;
    }

    // Update timestamp
    typedQuery.to.updatedAt = new Date();

    console.log('School during.set trigger - processed data:', typedQuery.to);
    return typedQuery;
  },

  remove: (...args: any[]) => {
    const [query, _multiple, _options] = args;
    const typedQuery = query as any;

    // Add any validation or cleanup logic for deletions
    console.log('School during.remove trigger called with query:', typedQuery);
    return typedQuery;
  }
};

// Trigger for creating schools
export const add: AddTrigger = (...args) => {
  const [query, _multiple, _options] = args;
  const typedQuery = query as any;

  // Ensure query.with exists
  if (!typedQuery.with) {
    return typedQuery;
  }

  // Handle both single object and array cases
  const processSchoolData = (schoolData: any) => {
    console.log('School creation trigger - processing data:', schoolData);

    // Set default values
    schoolData.isActive = schoolData.isActive !== false; // Default to true
    schoolData.studentCount = schoolData.studentCount || 0;
    schoolData.teacherCount = schoolData.teacherCount || 0;
    schoolData.createdAt = schoolData.createdAt || new Date();
    schoolData.updatedAt = schoolData.updatedAt || new Date();

    console.log('School trigger - processed school data:', schoolData);
    return schoolData;
  };

  // Handle array of schools
  if (Array.isArray(typedQuery.with)) {
    typedQuery.with = typedQuery.with.map(processSchoolData);
  } else {
    // Handle single school
    typedQuery.with = processSchoolData(typedQuery.with);
  }

  return typedQuery;
};

// Trigger for updating schools
export const set: SetTrigger = (...args) => {
  const [query, _multiple, _options] = args;
  const typedQuery = query as any;

  // Ensure query.to exists
  if (!typedQuery.to) {
    return typedQuery;
  }

  // Update timestamp
  typedQuery.to.updatedAt = new Date();

  console.log('School update trigger - processed data:', typedQuery.to);
  return typedQuery;
};

// Trigger for getting schools (can be used for access control)
export const get: GetTrigger = (...args) => {
  const [query, _multiple, _options] = args;
  const typedQuery = query as any;

  // Add any access control logic here if needed
  return typedQuery;
};

// Trigger for removing schools
export const remove: RemoveTrigger = (...args) => {
  const [query, _multiple, _options] = args;
  const typedQuery = query as any;

  // Add any validation or cleanup logic for deletions
  console.log('School removal trigger called with query:', typedQuery);
  return typedQuery;
};
