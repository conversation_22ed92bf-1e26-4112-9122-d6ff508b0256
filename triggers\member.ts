// triggers/member.ts
import type { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, RemoveTrigger } from 'blade/types';

// During triggers - required by Blade for write operations
export const during = {
  add: (...args: any[]) => {
    const [query, _multiple, _options] = args;
    const typedQuery = query as any;

    // Ensure query.with exists
    if (!typedQuery.with) {
      return typedQuery;
    }

    // Handle both single object and array cases
    const processMemberData = (memberData: any) => {
      console.log('Member during.add trigger - processing data:', memberData);

      // Set default values
      memberData.createdAt = memberData.createdAt || new Date();

      console.log('Member during.add trigger - processed data:', memberData);
      return memberData;
    };

    // Handle array of members
    if (Array.isArray(typedQuery.with)) {
      typedQuery.with = typedQuery.with.map(processMemberData);
    } else {
      // Handle single member
      typedQuery.with = processMemberData(typedQuery.with);
    }

    return typedQuery;
  },

  set: (...args: any[]) => {
    const [query, _multiple, _options] = args;
    const typedQuery = query as any;

    // Ensure query.to exists
    if (!typedQuery.to) {
      return typedQuery;
    }

    console.log('Member during.set trigger - processed data:', typedQuery.to);
    return typedQuery;
  },

  remove: (...args: any[]) => {
    const [query, _multiple, _options] = args;
    const typedQuery = query as any;

    // Add any validation or cleanup logic for deletions
    console.log('Member during.remove trigger called with query:', typedQuery);
    return typedQuery;
  }
};

// Trigger for creating members
export const add: AddTrigger = (...args) => {
  const [query, _multiple, _options] = args;
  const typedQuery = query as any;

  // Ensure query.with exists
  if (!typedQuery.with) {
    return typedQuery;
  }

  // Handle both single object and array cases
  const processMemberData = (memberData: any) => {
    console.log('Member creation trigger - processing data:', memberData);

    // Set default values
    memberData.createdAt = memberData.createdAt || new Date();

    console.log('Member trigger - processed member data:', memberData);
    return memberData;
  };

  // Handle array of members
  if (Array.isArray(typedQuery.with)) {
    typedQuery.with = typedQuery.with.map(processMemberData);
  } else {
    // Handle single member
    typedQuery.with = processMemberData(typedQuery.with);
  }

  return typedQuery;
};

// Trigger for updating members
export const set: SetTrigger = (...args) => {
  const [query, _multiple, _options] = args;
  const typedQuery = query as any;

  // Ensure query.to exists
  if (!typedQuery.to) {
    return typedQuery;
  }

  console.log('Member update trigger - processed data:', typedQuery.to);
  return typedQuery;
};

// Trigger for getting members (can be used for access control)
export const get: GetTrigger = (...args) => {
  const [query, _multiple, _options] = args;
  const typedQuery = query as any;

  // Add any access control logic here if needed
  return typedQuery;
};

// Trigger for removing members
export const remove: RemoveTrigger = (...args) => {
  const [query, _multiple, _options] = args;
  const typedQuery = query as any;

  // Add any validation or cleanup logic for deletions
  console.log('Member removal trigger called with query:', typedQuery);
  return typedQuery;
};
