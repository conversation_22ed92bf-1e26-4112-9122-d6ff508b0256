// triggers/teacher.ts
import type { <PERSON>d<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ger, Get<PERSON>rigger, RemoveTrigger } from 'blade/types';

// During triggers - required by <PERSON> for write operations
export const during = {
  add: (...args: any[]) => {
    const [query, _multiple, _options] = args;
    const typedQuery = query as any;

    // Ensure query.with exists
    if (!typedQuery.with) {
      return typedQuery;
    }

    // Handle both single object and array cases
    const processTeacherData = (teacherData: any) => {
      console.log('Teacher during.add trigger - processing data:', teacherData);

      // Set default values
      teacherData.bio = teacherData.bio || '';
      teacherData.isIndependent = teacherData.isIndependent !== false; // Default to true
      teacherData.isVerified = teacherData.isVerified || false;
      teacherData.createdAt = teacherData.createdAt || new Date();
      teacherData.updatedAt = teacherData.updatedAt || new Date();

      console.log('Teacher during.add trigger - processed data:', teacherData);
      return teacherData;
    };

    // Handle array of teachers
    if (Array.isArray(typedQuery.with)) {
      typedQuery.with = typedQuery.with.map(processTeacherData);
    } else {
      // Handle single teacher
      typedQuery.with = processTeacherData(typedQuery.with);
    }

    return typedQuery;
  },

  set: (...args: any[]) => {
    const [query, _multiple, _options] = args;
    const typedQuery = query as any;

    // Ensure query.to exists
    if (!typedQuery.to) {
      return typedQuery;
    }

    // Update timestamp
    typedQuery.to.updatedAt = new Date();

    console.log('Teacher during.set trigger - processed data:', typedQuery.to);
    return typedQuery;
  },

  remove: (...args: any[]) => {
    const [query, _multiple, _options] = args;
    const typedQuery = query as any;

    // Add any validation or cleanup logic for deletions
    console.log('Teacher during.remove trigger called with query:', typedQuery);
    return typedQuery;
  }
};

// Trigger for creating teachers
export const add: AddTrigger = (...args) => {
  const [query, _multiple, _options] = args;
  const typedQuery = query as any;

  // Ensure query.with exists
  if (!typedQuery.with) {
    return typedQuery;
  }

  // Handle both single object and array cases
  const processTeacherData = (teacherData: any) => {
    console.log('Teacher creation trigger - processing data:', teacherData);

    // Set default values
    teacherData.bio = teacherData.bio || '';
    teacherData.isIndependent = teacherData.isIndependent !== false; // Default to true
    teacherData.isVerified = teacherData.isVerified || false;
    teacherData.createdAt = teacherData.createdAt || new Date();
    teacherData.updatedAt = teacherData.updatedAt || new Date();

    console.log('Teacher trigger - processed teacher data:', teacherData);
    return teacherData;
  };

  // Handle array of teachers
  if (Array.isArray(typedQuery.with)) {
    typedQuery.with = typedQuery.with.map(processTeacherData);
  } else {
    // Handle single teacher
    typedQuery.with = processTeacherData(typedQuery.with);
  }

  return typedQuery;
};

// Trigger for updating teachers
export const set: SetTrigger = (...args) => {
  const [query, _multiple, _options] = args;
  const typedQuery = query as any;

  // Ensure query.to exists
  if (!typedQuery.to) {
    return typedQuery;
  }

  // Update timestamp
  typedQuery.to.updatedAt = new Date();

  console.log('Teacher update trigger - processed data:', typedQuery.to);
  return typedQuery;
};

// Trigger for getting teachers (can be used for access control)
export const get: GetTrigger = (...args) => {
  const [query, _multiple, _options] = args;
  const typedQuery = query as any;

  // Add any access control logic here if needed
  return typedQuery;
};

// Trigger for removing teachers
export const remove: RemoveTrigger = (...args) => {
  const [query, _multiple, _options] = args;
  const typedQuery = query as any;

  // Add any validation or cleanup logic for deletions
  console.log('Teacher removal trigger called with query:', typedQuery);
  return typedQuery;
};

// Trigger to run after teacher creation (synchronous)
export const afterAdd = async (query: any, _multiple: any, _options: any) => {
  const teacherData = query.with;

  if (!teacherData) {
    return [];
  }

  console.log('👨‍🏫 Teacher afterAdd trigger called for:', teacherData.userId);

  // Here you could add logic to:
  // - Send welcome notifications
  // - Create default classes or grade levels
  // - Update related records

  return [];
};
