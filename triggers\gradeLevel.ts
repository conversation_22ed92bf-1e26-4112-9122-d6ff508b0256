// triggers/gradeLevel.ts
import type { Add<PERSON><PERSON>ger, SetTrigger, RemoveTrigger } from 'blade/types';

// During triggers - required by Blade for write operations
export const during = {
  add: (...args: any[]) => {
    const [query, _multiple, _options] = args;
    const typedQuery = query as any;

    // Ensure query.with exists
    if (!typedQuery.with) {
      return typedQuery;
    }

    // Handle both single object and array cases
    const processGradeLevelData = (gradeLevelData: any) => {
      console.log('Grade Level during.add trigger - processing data:', gradeLevelData);

      // Auto-generate code if not provided
      if (!gradeLevelData.code && gradeLevelData.name) {
        // Generate code from name (e.g., "9th Grade" -> "9", "Beginner" -> "BEG")
        let code = gradeLevelData.name.toString();

        // Check if it's a traditional grade (contains "Grade")
        if (code.includes('Grade')) {
          const match = code.match(/(\d+)/);
          if (match) {
            code = match[1];
          }
        } else {
          // For non-traditional grades, use first 3 letters uppercase
          code = code.substring(0, 3).toUpperCase();
        }

        gradeLevelData.code = code;
      }

      // Set default sort order if not provided
      if (gradeLevelData.sortOrder === undefined || gradeLevelData.sortOrder === null) {
        // For traditional grades, use the numeric value
        if (gradeLevelData.code && /^\d+$/.test(gradeLevelData.code.toString())) {
          gradeLevelData.sortOrder = parseInt(gradeLevelData.code.toString());
        } else {
          // For non-traditional grades, use a high number to put them at the end
          gradeLevelData.sortOrder = 1000;
        }
      }

      // Ensure required fields
      gradeLevelData.isActive = gradeLevelData.isActive !== false; // Default to true
      gradeLevelData.createdAt = new Date();
      gradeLevelData.updatedAt = new Date();

      console.log('Grade Level during.add trigger - processed data:', gradeLevelData);

      return gradeLevelData;
    };

    // Handle array of grade levels
    if (Array.isArray(typedQuery.with)) {
      typedQuery.with = typedQuery.with.map(processGradeLevelData);
    } else {
      // Handle single grade level
      typedQuery.with = processGradeLevelData(typedQuery.with);
    }

    return typedQuery;
  },

  set: (...args: any[]) => {
    const [query, _multiple, _options] = args;
    const typedQuery = query as any;

    // Ensure query.to exists
    if (!typedQuery.to) {
      return typedQuery;
    }

    // Update timestamp
    typedQuery.to.updatedAt = new Date();

    console.log('Grade Level during.set trigger - processed data:', typedQuery.to);

    return typedQuery;
  },

  remove: (...args: any[]) => {
    const [query, _multiple, _options] = args;
    const typedQuery = query as any;

    // Add any validation or cleanup logic for deletions
    console.log('Grade Level during.remove trigger called with query:', typedQuery);
    return typedQuery;
  }
};

// Trigger for creating grade levels
export const add: AddTrigger = (...args) => {
  const [query, _multiple, _options] = args;
  const typedQuery = query as any;

  // Ensure query.with exists
  if (!typedQuery.with) {
    return typedQuery;
  }

  // Handle both single object and array cases
  const processGradeLevelData = (gradeLevelData: any) => {
    console.log('Grade Level creation trigger - processing data:', gradeLevelData);

    // Auto-generate code if not provided
    if (!gradeLevelData.code && gradeLevelData.name) {
      // Generate code from name (e.g., "9th Grade" -> "9", "Beginner" -> "BEG")
      let code = gradeLevelData.name.toString();

      // Check if it's a traditional grade (contains "Grade")
      if (code.includes('Grade')) {
        const match = code.match(/(\d+)/);
        if (match) {
          code = match[1];
        }
      } else {
        // For non-traditional grades, use first 3 letters uppercase
        code = code.substring(0, 3).toUpperCase();
      }

      gradeLevelData.code = code;
    }

    // Set default sort order if not provided
    if (gradeLevelData.sortOrder === undefined || gradeLevelData.sortOrder === null) {
      // For traditional grades, use the numeric value
      if (gradeLevelData.code && /^\d+$/.test(gradeLevelData.code.toString())) {
        gradeLevelData.sortOrder = parseInt(gradeLevelData.code.toString());
      } else {
        // For non-traditional grades, use a high number to put them at the end
        gradeLevelData.sortOrder = 1000;
      }
    }

    // Ensure required fields
    gradeLevelData.isActive = gradeLevelData.isActive !== false; // Default to true
    gradeLevelData.createdAt = new Date();
    gradeLevelData.updatedAt = new Date();

    console.log('Grade Level creation trigger - processed data:', gradeLevelData);

    return gradeLevelData;
  };

  // Handle array of grade levels
  if (Array.isArray(typedQuery.with)) {
    typedQuery.with = typedQuery.with.map(processGradeLevelData);
  } else {
    // Handle single grade level
    typedQuery.with = processGradeLevelData(typedQuery.with);
  }

  return typedQuery;
};

// Trigger for updating grade levels
export const set: SetTrigger = (...args) => {
  const [query, _multiple, _options] = args;
  const typedQuery = query as any;

  // Ensure query.to exists
  if (!typedQuery.to) {
    return typedQuery;
  }

  // Update timestamp
  typedQuery.to.updatedAt = new Date();

  console.log('Grade Level update trigger - processed data:', typedQuery.to);

  return typedQuery;
};

// Trigger for removing grade levels
export const remove: RemoveTrigger = (...args) => {
  const [query, _multiple, _options] = args;
  const typedQuery = query as any;

  // Add any validation or cleanup logic for deletions
  console.log('Grade Level removal trigger called with query:', typedQuery);
  return typedQuery;
};

// Trigger to run after grade level creation (synchronous)
export const afterAdd = async (query: any, _multiple: any, _options: any) => {
  const gradeLevelData = query.with;

  if (!gradeLevelData) {
    return [];
  }

  console.log('🎓 Grade Level afterAdd trigger called for:', gradeLevelData.name);

  // Here you could add logic to:
  // - Send notifications
  // - Update related records
  // - Log the creation

  return [];
};


