// triggers/session.ts
import type { Add<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ger, GetTrigger, RemoveTrigger } from 'blade/types';

// Trigger for creating sessions
export const add: AddTrigger = (...args) => {
  const [query, _multiple, _options] = args;
  const typedQuery = query as any;

  // Ensure query.with exists
  if (!typedQuery.with) {
    return typedQuery;
  }

  // Handle both single object and array cases
  const processSessionData = (sessionData: any) => {
    // Set default timestamps
    sessionData.createdAt = new Date();
    sessionData.updatedAt = new Date();

    // Set default expiration (24 hours from now)
    if (!sessionData.expiresAt) {
      const expiresAt = new Date();
      expiresAt.setHours(expiresAt.getHours() + 24);
      sessionData.expiresAt = expiresAt;
    }

    return sessionData;
  };

  // Handle array of sessions
  if (Array.isArray(typedQuery.with)) {
    typedQuery.with = typedQuery.with.map(processSessionData);
  } else {
    // Handle single session
    typedQuery.with = processSessionData(typedQuery.with);
  }

  return typedQuery;
};

// Trigger for updating sessions
export const set: SetTrigger = (...args) => {
  const [query, _multiple, _options] = args;
  const typedQuery = query as any;

  // Ensure query.to exists
  if (!typedQuery.to) {
    return typedQuery;
  }

  // Update timestamp
  typedQuery.to.updatedAt = new Date();

  return typedQuery;
};

// Trigger for getting sessions (validation)
export const get: GetTrigger = async (...args) => {
  const [query, _multiple, options] = args;
  const typedOptions = options as any;

  // If we're getting a session, validate that the associated user still exists
  const result = await typedOptions.client.get.sessions(query);

  if (result && !Array.isArray(result)) {
    // Single session - check if user exists
    if (result.userId) {
      try {
        const user = await typedOptions.client.get.user.with.id(result.userId);
        if (!user) {
          // User doesn't exist, remove the orphaned session
          console.log('Removing orphaned session for deleted user:', result.userId);
          await typedOptions.client.remove.session.with.id(result.id);
          return null;
        }
      } catch (error) {
        console.error('Error validating session user:', error);
        // If we can't validate, remove the session to be safe
        await typedOptions.client.remove.session.with.id(result.id);
        return null;
      }
    }
  } else if (Array.isArray(result)) {
    // Multiple sessions - filter out orphaned ones
    const validSessions = [];
    for (const session of result) {
      if (session.userId) {
        try {
          const user = await typedOptions.client.get.user.with.id(session.userId);
          if (user) {
            validSessions.push(session);
          } else {
            // Remove orphaned session
            console.log('Removing orphaned session for deleted user:', session.userId);
            await typedOptions.client.remove.session.with.id(session.id);
          }
        } catch (error) {
          console.error('Error validating session user:', error);
          // Remove session if we can't validate
          await typedOptions.client.remove.session.with.id(session.id);
        }
      } else {
        validSessions.push(session);
      }
    }
    return validSessions;
  }

  return result;
};

// Trigger for removing sessions (cleanup)
export const remove: RemoveTrigger = (...args) => {
  const [query, _multiple, _options] = args;
  const typedQuery = query as any;

  // Add any cleanup logic here if needed
  return typedQuery;
};