// triggers/educationalContext.ts
import type { <PERSON>d<PERSON><PERSON><PERSON>, SetTrigger, RemoveTrigger } from 'blade/types';

// During triggers - required by Blade for write operations
export const during = {
  add: (...args: any[]) => {
    const [query, _multiple, _options] = args;
    const typedQuery = query as any;

    // Ensure query.with exists
    if (!typedQuery.with) {
      return typedQuery;
    }

    // Handle both single object and array cases
    const processEducationalContextData = (contextData: any) => {
      console.log('Educational Context during.add trigger - processing data:', contextData);

      // Ensure required fields
      contextData.isActive = contextData.isActive !== false; // Default to true
      contextData.createdAt = new Date();
      contextData.updatedAt = new Date();

      console.log('Educational Context during.add trigger - processed data:', contextData);
      return contextData;
    };

    // Handle array of contexts
    if (Array.isArray(typedQuery.with)) {
      typedQuery.with = typedQuery.with.map(processEducationalContextData);
    } else {
      // Handle single context
      typedQuery.with = processEducationalContextData(typedQuery.with);
    }

    return typedQuery;
  },

  set: (...args: any[]) => {
    const [query, _multiple, _options] = args;
    const typedQuery = query as any;

    // Ensure query.to exists
    if (!typedQuery.to) {
      return typedQuery;
    }

    // Update timestamp
    typedQuery.to.updatedAt = new Date();

    console.log('Educational Context during.set trigger - processed data:', typedQuery.to);
    return typedQuery;
  },

  remove: (...args: any[]) => {
    const [query, _multiple, _options] = args;
    const typedQuery = query as any;

    // Add any validation or cleanup logic for deletions
    console.log('Educational Context during.remove trigger called with query:', typedQuery);
    return typedQuery;
  }
};

// Trigger for creating educational contexts
export const add: AddTrigger = (...args) => {
  const [query, _multiple, _options] = args;
  const typedQuery = query as any;

  // Ensure query.with exists
  if (!typedQuery.with) {
    return typedQuery;
  }

  // Handle both single object and array cases
  const processEducationalContextData = (contextData: any) => {
    console.log('Educational Context creation trigger - processing data:', contextData);

    // Ensure required fields
    contextData.isActive = contextData.isActive !== false; // Default to true
    contextData.createdAt = new Date();
    contextData.updatedAt = new Date();

    // Parse and validate defaultGradeLevels if provided
    if (contextData.defaultGradeLevels && typeof contextData.defaultGradeLevels === 'string') {
      try {
        const parsed = JSON.parse(contextData.defaultGradeLevels);
        if (Array.isArray(parsed)) {
          contextData.defaultGradeLevels = JSON.stringify(parsed);
        } else {
          contextData.defaultGradeLevels = JSON.stringify([]);
        }
      } catch (error) {
        console.error('Error parsing defaultGradeLevels:', error);
        contextData.defaultGradeLevels = JSON.stringify([]);
      }
    } else {
      contextData.defaultGradeLevels = JSON.stringify([]);
    }

    console.log('Educational Context creation trigger - processed data:', contextData);

    return contextData;
  };

  // Handle array of educational contexts
  if (Array.isArray(typedQuery.with)) {
    typedQuery.with = typedQuery.with.map(processEducationalContextData);
  } else {
    // Handle single educational context
    typedQuery.with = processEducationalContextData(typedQuery.with);
  }

  return typedQuery;
};

// Trigger for updating educational contexts
export const set: SetTrigger = (...args) => {
  const [query, _multiple, _options] = args;
  const typedQuery = query as any;

  // Ensure query.to exists
  if (!typedQuery.to) {
    return typedQuery;
  }

  // Update timestamp
  typedQuery.to.updatedAt = new Date();

  console.log('Educational Context update trigger - processed data:', typedQuery.to);

  return typedQuery;
};

// Trigger for removing educational contexts
export const remove: RemoveTrigger = (...args) => {
  const [query, _multiple, _options] = args;
  const typedQuery = query as any;

  // Add any validation or cleanup logic for deletions
  console.log('Educational Context removal trigger called with query:', typedQuery);
  return typedQuery;
};

// Trigger to run after educational context creation (synchronous)
export const afterAdd = async (query: any, _multiple: any, _options: any) => {
  const contextData = query.with;

  if (!contextData) {
    return [];
  }

  console.log('🏫 Educational Context afterAdd trigger called for:', contextData.name);
  
  // Here you could add logic to:
  // - Create default grade levels for this context
  // - Send notifications
  // - Update related records
  
  return [];
};
