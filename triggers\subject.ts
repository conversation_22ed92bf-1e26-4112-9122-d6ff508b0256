// triggers/subject.ts
import type { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, RemoveTrigger } from 'blade/types';

// During triggers - required by Blade for write operations
export const during = {
  add: (...args: any[]) => {
    const [query, _multiple, _options] = args;
    const typedQuery = query as any;

    // Ensure query.with exists
    if (!typedQuery.with) {
      return typedQuery;
    }

    // Handle both single object and array cases
    const processSubjectData = (subjectData: any) => {
      console.log('Subject during.add trigger - processing data:', subjectData);

      // Auto-generate code if not provided
      if (!subjectData.code && subjectData.name) {
        subjectData.code = subjectData.name
          .toUpperCase()
          .replace(/\s+/g, '_')
          .replace(/[^A-Z0-9_]/g, '')
          .substring(0, 10);
      }

      // Set default values
      subjectData.isActive = subjectData.isActive !== false; // Default to true
      subjectData.createdAt = subjectData.createdAt || new Date();

      console.log('Subject during.add trigger - processed data:', subjectData);
      return subjectData;
    };

    // Handle array of subjects
    if (Array.isArray(typedQuery.with)) {
      typedQuery.with = typedQuery.with.map(processSubjectData);
    } else {
      // Handle single subject
      typedQuery.with = processSubjectData(typedQuery.with);
    }

    return typedQuery;
  },

  set: (...args: any[]) => {
    const [query, _multiple, _options] = args;
    const typedQuery = query as any;

    // Ensure query.to exists
    if (!typedQuery.to) {
      return typedQuery;
    }

    // Update code if name is being changed and code is not already provided
    if (typedQuery.to.name && !typedQuery.to.code) {
      typedQuery.to.code = typedQuery.to.name
        .toUpperCase()
        .replace(/\s+/g, '_')
        .replace(/[^A-Z0-9_]/g, '')
        .substring(0, 10);
    }

    console.log('Subject during.set trigger - processed data:', typedQuery.to);
    return typedQuery;
  },

  remove: (...args: any[]) => {
    const [query, _multiple, _options] = args;
    const typedQuery = query as any;

    // Add any validation or cleanup logic for deletions
    console.log('Subject during.remove trigger called with query:', typedQuery);
    return typedQuery;
  }
};

// Trigger for creating subjects
export const add: AddTrigger = (...args) => {
  const [query, _multiple, _options] = args;
  const typedQuery = query as any;

  // Ensure query.with exists
  if (!typedQuery.with) {
    return typedQuery;
  }

  // Handle both single object and array cases
  const processSubjectData = (subjectData: any) => {
    console.log('Subject creation trigger - processing data:', subjectData);

    // Auto-generate code if not provided
    if (!subjectData.code && subjectData.name) {
      subjectData.code = subjectData.name
        .toUpperCase()
        .replace(/\s+/g, '_')
        .replace(/[^A-Z0-9_]/g, '')
        .substring(0, 10);
    }

    // Set default values
    subjectData.isActive = subjectData.isActive !== false; // Default to true
    subjectData.createdAt = subjectData.createdAt || new Date();

    console.log('Subject trigger - processed subject data:', subjectData);
    return subjectData;
  };

  // Handle array of subjects
  if (Array.isArray(typedQuery.with)) {
    typedQuery.with = typedQuery.with.map(processSubjectData);
  } else {
    // Handle single subject
    typedQuery.with = processSubjectData(typedQuery.with);
  }

  return typedQuery;
};

// Trigger for updating subjects
export const set: SetTrigger = (...args) => {
  const [query, _multiple, _options] = args;
  const typedQuery = query as any;

  // Ensure query.to exists
  if (!typedQuery.to) {
    return typedQuery;
  }

  // Update code if name is being changed and code is not already provided
  if (typedQuery.to.name && !typedQuery.to.code) {
    typedQuery.to.code = typedQuery.to.name
      .toUpperCase()
      .replace(/\s+/g, '_')
      .replace(/[^A-Z0-9_]/g, '')
      .substring(0, 10);
  }

  console.log('Subject update trigger - processed data:', typedQuery.to);
  return typedQuery;
};

// Trigger for getting subjects (can be used for access control)
export const get: GetTrigger = (...args) => {
  const [query, _multiple, _options] = args;
  const typedQuery = query as any;

  // Add any access control logic here if needed
  return typedQuery;
};

// Trigger for removing subjects
export const remove: RemoveTrigger = (...args) => {
  const [query, _multiple, _options] = args;
  const typedQuery = query as any;

  // Add any validation or cleanup logic for deletions
  console.log('Subject removal trigger called with query:', typedQuery);
  return typedQuery;
};
