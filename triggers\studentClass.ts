// triggers/studentClass.ts
import type { <PERSON>d<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ger, <PERSON>Trigger, RemoveTrigger } from 'blade/types';

// During triggers - required by <PERSON> for write operations
export const during = {
  add: (...args: any[]) => {
    const [query, _multiple, _options] = args;
    const typedQuery = query as any;

    // Ensure query.with exists
    if (!typedQuery.with) {
      return typedQuery;
    }

    // Handle both single object and array cases
    const processStudentClassData = (enrollmentData: any) => {
      console.log('StudentClass during.add trigger - processing data:', enrollmentData);

      // Set default values
      enrollmentData.enrolledAt = enrollmentData.enrolledAt || new Date();
      enrollmentData.status = enrollmentData.status || 'active';

      console.log('StudentClass during.add trigger - processed data:', enrollmentData);
      return enrollmentData;
    };

    // Handle array of enrollments
    if (Array.isArray(typedQuery.with)) {
      typedQuery.with = typedQuery.with.map(processStudentClassData);
    } else {
      // Handle single enrollment
      typedQuery.with = processStudentClassData(typedQuery.with);
    }

    return typedQuery;
  },

  set: (...args: any[]) => {
    const [query, _multiple, _options] = args;
    const typedQuery = query as any;

    // Ensure query.to exists
    if (!typedQuery.to) {
      return typedQuery;
    }

    // Set completion timestamp if status is being changed to completed
    if (typedQuery.to.status === 'completed' && !typedQuery.to.completedAt) {
      typedQuery.to.completedAt = new Date();
    }

    console.log('StudentClass during.set trigger - processed data:', typedQuery.to);
    return typedQuery;
  },

  remove: (...args: any[]) => {
    const [query, _multiple, _options] = args;
    const typedQuery = query as any;

    // Add any validation or cleanup logic for deletions
    console.log('StudentClass during.remove trigger called with query:', typedQuery);
    return typedQuery;
  }
};

// Trigger for creating student-class enrollments
export const add: AddTrigger = (...args) => {
  const [query, _multiple, _options] = args;
  const typedQuery = query as any;

  // Ensure query.with exists
  if (!typedQuery.with) {
    return typedQuery;
  }

  // Handle both single object and array cases
  const processStudentClassData = (enrollmentData: any) => {
    console.log('StudentClass creation trigger - processing data:', enrollmentData);

    // Set default values
    enrollmentData.enrolledAt = enrollmentData.enrolledAt || new Date();
    enrollmentData.status = enrollmentData.status || 'active';

    console.log('StudentClass trigger - processed enrollment data:', enrollmentData);
    return enrollmentData;
  };

  // Handle array of enrollments
  if (Array.isArray(typedQuery.with)) {
    typedQuery.with = typedQuery.with.map(processStudentClassData);
  } else {
    // Handle single enrollment
    typedQuery.with = processStudentClassData(typedQuery.with);
  }

  return typedQuery;
};

// Trigger for updating student-class enrollments
export const set: SetTrigger = (...args) => {
  const [query, _multiple, _options] = args;
  const typedQuery = query as any;

  // Ensure query.to exists
  if (!typedQuery.to) {
    return typedQuery;
  }

  // Set completion timestamp if status is being changed to completed
  if (typedQuery.to.status === 'completed' && !typedQuery.to.completedAt) {
    typedQuery.to.completedAt = new Date();
  }

  console.log('StudentClass update trigger - processed data:', typedQuery.to);
  return typedQuery;
};

// Trigger for getting student-class enrollments (can be used for access control)
export const get: GetTrigger = (...args) => {
  const [query, _multiple, _options] = args;
  const typedQuery = query as any;

  // Add any access control logic here if needed
  return typedQuery;
};

// Trigger for removing student-class enrollments
export const remove: RemoveTrigger = (...args) => {
  const [query, _multiple, _options] = args;
  const typedQuery = query as any;

  // Add any validation or cleanup logic for deletions
  console.log('StudentClass removal trigger called with query:', typedQuery);
  return typedQuery;
};
