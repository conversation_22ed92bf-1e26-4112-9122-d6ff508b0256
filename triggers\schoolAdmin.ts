// triggers/schoolAdmin.ts
import type { Add<PERSON><PERSON><PERSON>, SetTrigger, GetTrigger, RemoveTrigger } from 'blade/types';

// During triggers - required by <PERSON> for write operations
export const during = {
  add: (...args: any[]) => {
    const [query, _multiple, _options] = args;
    const typedQuery = query as any;

    // Ensure query.with exists
    if (!typedQuery.with) {
      return typedQuery;
    }

    // Handle both single object and array cases
    const processSchoolAdminData = (schoolAdminData: any) => {
      console.log('SchoolAdmin during.add trigger - processing data:', schoolAdminData);

      // Set default values
      schoolAdminData.createdAt = schoolAdminData.createdAt || new Date();
      schoolAdminData.updatedAt = schoolAdminData.updatedAt || new Date();

      console.log('SchoolAdmin during.add trigger - processed data:', schoolAdminData);
      return schoolAdminData;
    };

    // Handle array of school admins
    if (Array.isArray(typedQuery.with)) {
      typedQuery.with = typedQuery.with.map(processSchoolAdminData);
    } else {
      // Handle single school admin
      typedQuery.with = processSchoolAdminData(typedQuery.with);
    }

    return typedQuery;
  },

  set: (...args: any[]) => {
    const [query, _multiple, _options] = args;
    const typedQuery = query as any;

    // Ensure query.to exists
    if (!typedQuery.to) {
      return typedQuery;
    }

    // Update timestamp
    typedQuery.to.updatedAt = new Date();

    console.log('SchoolAdmin during.set trigger - processed data:', typedQuery.to);
    return typedQuery;
  },

  remove: (...args: any[]) => {
    const [query, _multiple, _options] = args;
    const typedQuery = query as any;

    // Add any validation or cleanup logic for deletions
    console.log('SchoolAdmin during.remove trigger called with query:', typedQuery);
    return typedQuery;
  }
};

// Trigger for creating school admins
export const add: AddTrigger = (...args) => {
  const [query, _multiple, _options] = args;
  const typedQuery = query as any;

  // Ensure query.with exists
  if (!typedQuery.with) {
    return typedQuery;
  }

  // Handle both single object and array cases
  const processSchoolAdminData = (schoolAdminData: any) => {
    console.log('SchoolAdmin creation trigger - processing data:', schoolAdminData);

    // Set default values
    schoolAdminData.createdAt = schoolAdminData.createdAt || new Date();
    schoolAdminData.updatedAt = schoolAdminData.updatedAt || new Date();

    console.log('SchoolAdmin trigger - processed school admin data:', schoolAdminData);
    return schoolAdminData;
  };

  // Handle array of school admins
  if (Array.isArray(typedQuery.with)) {
    typedQuery.with = typedQuery.with.map(processSchoolAdminData);
  } else {
    // Handle single school admin
    typedQuery.with = processSchoolAdminData(typedQuery.with);
  }

  return typedQuery;
};

// Trigger for updating school admins
export const set: SetTrigger = (...args) => {
  const [query, _multiple, _options] = args;
  const typedQuery = query as any;

  // Ensure query.to exists
  if (!typedQuery.to) {
    return typedQuery;
  }

  // Update timestamp
  typedQuery.to.updatedAt = new Date();

  console.log('SchoolAdmin update trigger - processed data:', typedQuery.to);
  return typedQuery;
};

// Trigger for getting school admins (can be used for access control)
export const get: GetTrigger = (...args) => {
  const [query, _multiple, _options] = args;
  const typedQuery = query as any;

  // Add any access control logic here if needed
  return typedQuery;
};

// Trigger for removing school admins
export const remove: RemoveTrigger = (...args) => {
  const [query, _multiple, _options] = args;
  const typedQuery = query as any;

  // Add any validation or cleanup logic for deletions
  console.log('SchoolAdmin removal trigger called with query:', typedQuery);
  return typedQuery;
};
