// triggers/teacherSchool.ts
import type { <PERSON>d<PERSON><PERSON><PERSON>, <PERSON><PERSON>rigger, <PERSON><PERSON>rigger, RemoveTrigger } from 'blade/types';

// During triggers - required by <PERSON> for write operations
export const during = {
  add: (...args: any[]) => {
    const [query, _multiple, _options] = args;
    const typedQuery = query as any;

    // Ensure query.with exists
    if (!typedQuery.with) {
      return typedQuery;
    }

    // Handle both single object and array cases
    const processTeacherSchoolData = (relationshipData: any) => {
      console.log('TeacherSchool during.add trigger - processing data:', relationshipData);

      // Set default values
      relationshipData.status = relationshipData.status || 'active';
      relationshipData.invitedAt = relationshipData.invitedAt || new Date();

      console.log('TeacherSchool during.add trigger - processed data:', relationshipData);
      return relationshipData;
    };

    // Handle array of relationships
    if (Array.isArray(typedQuery.with)) {
      typedQuery.with = typedQuery.with.map(processTeacherSchoolData);
    } else {
      // Handle single relationship
      typedQuery.with = processTeacherSchoolData(typedQuery.with);
    }

    return typedQuery;
  },

  set: (...args: any[]) => {
    const [query, _multiple, _options] = args;
    const typedQuery = query as any;

    // Ensure query.to exists
    if (!typedQuery.to) {
      return typedQuery;
    }

    // Set join timestamp if status is being changed to active
    if (typedQuery.to.status === 'active' && !typedQuery.to.joinedAt) {
      typedQuery.to.joinedAt = new Date();
    }

    console.log('TeacherSchool during.set trigger - processed data:', typedQuery.to);
    return typedQuery;
  },

  remove: (...args: any[]) => {
    const [query, _multiple, _options] = args;
    const typedQuery = query as any;

    // Add any validation or cleanup logic for deletions
    console.log('TeacherSchool during.remove trigger called with query:', typedQuery);
    return typedQuery;
  }
};

// Trigger for creating teacher-school relationships
export const add: AddTrigger = (...args) => {
  const [query, _multiple, _options] = args;
  const typedQuery = query as any;

  // Ensure query.with exists
  if (!typedQuery.with) {
    return typedQuery;
  }

  // Handle both single object and array cases
  const processTeacherSchoolData = (relationshipData: any) => {
    console.log('TeacherSchool creation trigger - processing data:', relationshipData);

    // Set default values
    relationshipData.status = relationshipData.status || 'active';
    relationshipData.invitedAt = relationshipData.invitedAt || new Date();

    console.log('TeacherSchool trigger - processed relationship data:', relationshipData);
    return relationshipData;
  };

  // Handle array of relationships
  if (Array.isArray(typedQuery.with)) {
    typedQuery.with = typedQuery.with.map(processTeacherSchoolData);
  } else {
    // Handle single relationship
    typedQuery.with = processTeacherSchoolData(typedQuery.with);
  }

  return typedQuery;
};

// Trigger for updating teacher-school relationships
export const set: SetTrigger = (...args) => {
  const [query, _multiple, _options] = args;
  const typedQuery = query as any;

  // Ensure query.to exists
  if (!typedQuery.to) {
    return typedQuery;
  }

  // Set join timestamp if status is being changed to active
  if (typedQuery.to.status === 'active' && !typedQuery.to.joinedAt) {
    typedQuery.to.joinedAt = new Date();
  }

  console.log('TeacherSchool update trigger - processed data:', typedQuery.to);
  return typedQuery;
};

// Trigger for getting teacher-school relationships (can be used for access control)
export const get: GetTrigger = (...args) => {
  const [query, _multiple, _options] = args;
  const typedQuery = query as any;

  // Add any access control logic here if needed
  return typedQuery;
};

// Trigger for removing teacher-school relationships
export const remove: RemoveTrigger = (...args) => {
  const [query, _multiple, _options] = args;
  const typedQuery = query as any;

  // Add any validation or cleanup logic for deletions
  console.log('TeacherSchool removal trigger called with query:', typedQuery);
  return typedQuery;
};
