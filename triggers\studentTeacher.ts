// triggers/studentTeacher.ts
import type { <PERSON>d<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ger, <PERSON><PERSON>rigger, RemoveTrigger } from 'blade/types';

// During triggers - required by <PERSON> for write operations
export const during = {
  add: (...args: any[]) => {
    const [query, _multiple, _options] = args;
    const typedQuery = query as any;

    // Ensure query.with exists
    if (!typedQuery.with) {
      return typedQuery;
    }

    // Handle both single object and array cases
    const processStudentTeacherData = (relationshipData: any) => {
      console.log('StudentTeacher during.add trigger - processing data:', relationshipData);

      // Set default values
      relationshipData.assignedAt = relationshipData.assignedAt || new Date();
      relationshipData.status = relationshipData.status || 'active';

      console.log('StudentTeacher during.add trigger - processed data:', relationshipData);
      return relationshipData;
    };

    // Handle array of relationships
    if (Array.isArray(typedQuery.with)) {
      typedQuery.with = typedQuery.with.map(processStudentTeacherData);
    } else {
      // Handle single relationship
      typedQuery.with = processStudentTeacherData(typedQuery.with);
    }

    return typedQuery;
  },

  set: (...args: any[]) => {
    const [query, _multiple, _options] = args;
    const typedQuery = query as any;

    // Ensure query.to exists
    if (!typedQuery.to) {
      return typedQuery;
    }

    // No specific processing needed for updates
    console.log('StudentTeacher during.set trigger - processed data:', typedQuery.to);
    return typedQuery;
  },

  remove: (...args: any[]) => {
    const [query, _multiple, _options] = args;
    const typedQuery = query as any;

    // Add any validation or cleanup logic for deletions
    console.log('StudentTeacher during.remove trigger called with query:', typedQuery);
    return typedQuery;
  }
};

// Trigger for creating student-teacher relationships
export const add: AddTrigger = (...args) => {
  const [query, _multiple, _options] = args;
  const typedQuery = query as any;

  // Ensure query.with exists
  if (!typedQuery.with) {
    return typedQuery;
  }

  // Handle both single object and array cases
  const processStudentTeacherData = (relationshipData: any) => {
    console.log('StudentTeacher creation trigger - processing data:', relationshipData);

    // Set default values
    relationshipData.assignedAt = relationshipData.assignedAt || new Date();
    relationshipData.status = relationshipData.status || 'active';

    console.log('StudentTeacher trigger - processed relationship data:', relationshipData);
    return relationshipData;
  };

  // Handle array of relationships
  if (Array.isArray(typedQuery.with)) {
    typedQuery.with = typedQuery.with.map(processStudentTeacherData);
  } else {
    // Handle single relationship
    typedQuery.with = processStudentTeacherData(typedQuery.with);
  }

  return typedQuery;
};

// Trigger for updating student-teacher relationships
export const set: SetTrigger = (...args) => {
  const [query, _multiple, _options] = args;
  const typedQuery = query as any;

  // Ensure query.to exists
  if (!typedQuery.to) {
    return typedQuery;
  }

  // No specific processing needed for updates
  console.log('StudentTeacher update trigger - processed data:', typedQuery.to);
  return typedQuery;
};

// Trigger for getting student-teacher relationships (can be used for access control)
export const get: GetTrigger = (...args) => {
  const [query, _multiple, _options] = args;
  const typedQuery = query as any;

  // Add any access control logic here if needed
  return typedQuery;
};

// Trigger for removing student-teacher relationships
export const remove: RemoveTrigger = (...args) => {
  const [query, _multiple, _options] = args;
  const typedQuery = query as any;

  // Add any validation or cleanup logic for deletions
  console.log('StudentTeacher removal trigger called with query:', typedQuery);
  return typedQuery;
};

// Trigger to run after student-teacher relationship creation (synchronous)
export const afterAdd = async (query: any, _multiple: any, _options: any) => {
  const relationshipData = query.with;

  if (!relationshipData) {
    return [];
  }

  console.log('🔗 StudentTeacher afterAdd trigger called for:', {
    studentId: relationshipData.studentId,
    teacherId: relationshipData.teacherId
  });

  // Here you could add logic to:
  // - Send notifications to both student and teacher
  // - Update enrollment counts
  // - Create default assignments or grades

  return [];
};
