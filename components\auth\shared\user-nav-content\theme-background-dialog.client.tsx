import React, { useState, useCallback, useRef, useEffect, useMemo, forwardRef } from 'react';
import { Dialog } from '@base-ui-components/react/dialog';
import { <PERSON>, Palette, <PERSON>, <PERSON>, <PERSON>, Monitor } from 'lucide-react';
import { cn } from '../../../../lib/utils';
import { useTheme } from '../../../providers/theme-provider.client';
// Add this import
// Add this import back
import { useAutoHideScrollbar } from '../../../../hooks/use-auto-hide-scrollbar';

// Define background themes with more visible light mode colors
export interface BackgroundTheme {
  id: string;
  name: string;
  lightStyle: React.CSSProperties;
  darkStyle: React.CSSProperties;
  preview: {
    light: string;
    dark: string;
  };
}

export const backgroundThemes: BackgroundTheme[] = [
  {
    id: 'default',
    name: 'Default',
    lightStyle: {
      background: "linear-gradient(to bottom, #f2f2f2, #e8e8e8, #f2f2f2)"
    },
    darkStyle: {
      background: "linear-gradient(to bottom, #101012, #18181a, #101012)"
    },
    preview: {
      light: "linear-gradient(to bottom, #f2f2f2, #e8e8e8, #f2f2f2)",
      dark: "linear-gradient(to bottom, #101012, #18181a, #101012)"
    }
  },
  {
    id: 'horizon-glow-bottom',
    name: 'Horizon Glow (Bottom)',
    lightStyle: {
      background: "linear-gradient(to bottom, #f2f2f2 20%, #eaf4f7 100%)"
    },
    darkStyle: {
      background: "linear-gradient(to bottom, #101012 20%, #0e1621 100%)"
    },
    preview: {
      light: "linear-gradient(to bottom, #f2f2f2 20%, #eaf4f7 100%)",
      dark: "linear-gradient(to bottom, #101012 20%, #0e1621 100%)"
    }
  },
  {
    id: 'crimson-depth-bottom',
    name: 'Crimson Depth (Bottom)',
    lightStyle: {
      background: "linear-gradient(to bottom, #f2f2f2 20%, #f7e8ea 100%)"
    },
    darkStyle: {
      background: "linear-gradient(to bottom, #101012 20%, #1f1315 100%)"
    },
    preview: {
      light: "linear-gradient(to bottom, #f2f2f2 20%, #f7e8ea 100%)",
      dark: "linear-gradient(to bottom, #101012 20%, #1f1315 100%)"
    }
  },
  {
    id: 'emerald-void-bottom',
    name: 'Emerald Void (Bottom)',
    lightStyle: {
      background: "linear-gradient(to bottom, #f2f2f2 20%, #e8f3e9 100%)"
    },
    darkStyle: {
      background: "linear-gradient(to bottom, #101012 20%, #0f1a11 100%)"
    },
    preview: {
      light: "linear-gradient(to bottom, #f2f2f2 20%, #e8f3e9 100%)",
      dark: "linear-gradient(to bottom, #101012 20%, #0f1a11 100%)"
    }
  },
  {
    id: 'violet-abyss-bottom',
    name: 'Violet Abyss (Bottom)',
    lightStyle: {
      background: "linear-gradient(to bottom, #f2f2f2 20%, #f0e7f4 100%)"
    },
    darkStyle: {
      background: "linear-gradient(to bottom, #101012 20%, #1a1321 100%)"
    },
    preview: {
      light: "linear-gradient(to bottom, #f2f2f2 20%, #f0e7f4 100%)",
      dark: "linear-gradient(to bottom, #101012 20%, #1a1321 100%)"
    }
  },
  {
    id: 'azure-depths-bottom',
    name: 'Azure Depths (Bottom)',
    lightStyle: {
      background: "linear-gradient(to bottom, #f2f2f2 20%, #eaebf5 100%)"
    },
    darkStyle: {
      background: "linear-gradient(to bottom, #101012 20%, #0e0e2a 100%)"
    },
    preview: {
      light: "linear-gradient(to bottom, #f2f2f2 20%, #eaebf5 100%)",
      dark: "linear-gradient(to bottom, #101012 20%, #0e0e2a 100%)"
    }
  },
  {
    id: 'orchid-depths-bottom',
    name: 'Orchid Depths (Bottom)',
    lightStyle: {
      background: "linear-gradient(to bottom, #f2f2f2 20%, #f8e8ed 100%)"
    },
    darkStyle: {
      background: "linear-gradient(to bottom, #101012 20%, #2a1220 100%)"
    },
    preview: {
      light: "linear-gradient(to bottom, #f2f2f2 20%, #f8e8ed 100%)",
      dark: "linear-gradient(to bottom, #101012 20%, #2a1220 100%)"
    }
  },
  {
    id: 'horizon-glow-top',
    name: 'Horizon Glow (Top)',
    lightStyle: {
      background: "linear-gradient(to bottom, #eaf4f7 0%, #f2f2f2 80%)"
    },
    darkStyle: {
      background: "linear-gradient(to bottom, #0e1621 0%, #101012 80%)"
    },
    preview: {
      light: "linear-gradient(to bottom, #eaf4f7 0%, #f2f2f2 80%)",
      dark: "linear-gradient(to bottom, #0e1621 0%, #101012 80%)"
    }
  },
  {
    id: 'crimson-depth-top',
    name: 'Crimson Depth (Top)',
    lightStyle: {
      background: "linear-gradient(to bottom, #f7e8ea 0%, #f2f2f2 80%)"
    },
    darkStyle: {
      background: "linear-gradient(to bottom, #1f1315 0%, #101012 80%)"
    },
    preview: {
      light: "linear-gradient(to bottom, #f7e8ea 0%, #f2f2f2 80%)",
      dark: "linear-gradient(to bottom, #1f1315 0%, #101012 80%)"
    }
  },
  {
    id: 'emerald-void-top',
    name: 'Emerald Void (Top)',
    lightStyle: {
      background: "linear-gradient(to bottom, #e8f3e9 0%, #f2f2f2 80%)"
    },
    darkStyle: {
      background: "linear-gradient(to bottom, #0f1a11 0%, #101012 80%)"
    },
    preview: {
      light: "linear-gradient(to bottom, #e8f3e9 0%, #f2f2f2 80%)",
      dark: "linear-gradient(to bottom, #0f1a11 0%, #101012 80%)"
    }
  },
  {
    id: 'violet-abyss-top',
    name: 'Violet Abyss (Top)',
    lightStyle: {
      background: "linear-gradient(to bottom, #f0e7f4 0%, #f2f2f2 80%)"
    },
    darkStyle: {
      background: "linear-gradient(to bottom, #1a1321 0%, #101012 80%)"
    },
    preview: {
      light: "linear-gradient(to bottom, #f0e7f4 0%, #f2f2f2 80%)",
      dark: "linear-gradient(to bottom, #1a1321 0%, #101012 80%)"
    }
  },
  {
    id: 'azure-depths-top',
    name: 'Azure Depths (Top)',
    lightStyle: {
      background: "linear-gradient(to bottom, #eaebf5 0%, #f2f2f2 80%)"
    },
    darkStyle: {
      background: "linear-gradient(to bottom, #0e0e2a 0%, #101012 80%)"
    },
    preview: {
      light: "linear-gradient(to bottom, #eaebf5 0%, #f2f2f2 80%)",
      dark: "linear-gradient(to bottom, #0e0e2a 0%, #101012 80%)"
    }
  },
  {
    id: 'orchid-depths-top',
    name: 'Orchid Depths (Top)',
    lightStyle: {
      background: "linear-gradient(to bottom, #f8e8ed 0%, #f2f2f2 80%)"
    },
    darkStyle: {
      background: "linear-gradient(to bottom, #2a1220 0%, #101012 80%)"
    },
    preview: {
      light: "linear-gradient(to bottom, #f8e8ed 0%, #f2f2f2 80%)",
      dark: "linear-gradient(to bottom, #2a1220 0%, #101012 80%)"
    }
  }
];

export type Theme = 'light' | 'dark' | 'system';

interface ThemeBackgroundDialogProps {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
}

export const ThemeBackgroundDialog = forwardRef<HTMLDivElement, ThemeBackgroundDialogProps>(({
  isOpen,
  onOpenChange
}, ref) => {
  const { theme, setTheme, backgroundTheme, setBackgroundTheme, actualTheme } = useTheme();
  
  const [selectedTheme, setSelectedTheme] = useState<Theme>(theme);
  // Separate background selections for light and dark modes
  const [lightModeBackground, setLightModeBackground] = useState(backgroundTheme);
  const [darkModeBackground, setDarkModeBackground] = useState(backgroundTheme);
  const [isLoading, setIsLoading] = useState(false);

  // Add ref for the scrollable content container
  const scrollContainerRef = useRef<HTMLDivElement>(null);
  
  // Add the auto-hide scrollbar functionality
  useAutoHideScrollbar(scrollContainerRef);

  // Auto-focus the scrollable content when dialog opens (similar to enhanced sidebar)
  useEffect(() => {
    if (isOpen) {
      // Use multiple timers to ensure we override the dialog's focus management
      const timer1 = setTimeout(() => {
        const scrollContainer = scrollContainerRef.current;
        if (scrollContainer) {
          scrollContainer.focus();
        }
      }, 50); // First attempt - early

      const timer2 = setTimeout(() => {
        const scrollContainer = scrollContainerRef.current;
        if (scrollContainer) {
          // Force focus and ensure it's scrollable
          scrollContainer.focus();
          scrollContainer.scrollTop = 0; // Reset scroll position
        }
      }, 200); // Second attempt - after dialog animation

      const timer3 = setTimeout(() => {
        const scrollContainer = scrollContainerRef.current;
        if (scrollContainer) {
          // Final attempt to ensure focus
          scrollContainer.focus();
        }
      }, 400); // Final attempt - ensuring focus is set

      return () => {
        clearTimeout(timer1);
        clearTimeout(timer2);
        clearTimeout(timer3);
      };
    }
  }, [isOpen]);

  // Initialize backgrounds based on current theme when dialog opens
  useEffect(() => {
    if (isOpen) {
      setSelectedTheme(theme);
      // Initialize both light and dark mode backgrounds to current selection
      setLightModeBackground(backgroundTheme);
      setDarkModeBackground(backgroundTheme);
    }
  }, [isOpen, theme, backgroundTheme]);

  // Determine preview mode
  const previewMode = useMemo(() => {
    if (selectedTheme === 'system') {
      return actualTheme;
    }
    return selectedTheme as 'light' | 'dark';
  }, [selectedTheme, actualTheme]);

  // Get the current background selection based on preview mode
  const currentBackgroundSelection = useMemo(() => {
    return previewMode === 'dark' ? darkModeBackground : lightModeBackground;
  }, [previewMode, lightModeBackground, darkModeBackground]);

  // Helper function to get preview style object for a theme
  const getPreviewStyle = useCallback((bgTheme: BackgroundTheme): React.CSSProperties => {
    return {
      background: previewMode === 'dark' ? bgTheme.preview.dark : bgTheme.preview.light
    };
  }, [previewMode]);

  const handleThemeChange = useCallback((newTheme: Theme) => {
    setSelectedTheme(newTheme);
  }, []);

  const handleBackgroundChange = useCallback((backgroundId: string) => {
    // Update the appropriate background selection based on current preview mode
    if (previewMode === 'dark') {
      setDarkModeBackground(backgroundId);
    } else {
      setLightModeBackground(backgroundId);
    }
  }, [previewMode]);

  const handleSave = useCallback(async () => {
    setIsLoading(true);
    
    try {
      // Simulate a brief delay for UX
      await new Promise(resolve => setTimeout(resolve, 200));
      
      // Apply theme change
      setTheme(selectedTheme);
      
      // Apply the appropriate background based on the final resolved theme
      const finalResolvedTheme = selectedTheme === 'system' ? actualTheme : selectedTheme;
      const backgroundToApply = finalResolvedTheme === 'dark' ? darkModeBackground : lightModeBackground;
      setBackgroundTheme(backgroundToApply);
      
      // Close dialog
      onOpenChange(false);
    } catch (error) {
      console.error('Failed to save theme settings:', error);
    } finally {
      setIsLoading(false);
    }
  }, [selectedTheme, lightModeBackground, darkModeBackground, setTheme, setBackgroundTheme, onOpenChange, actualTheme]);

  const getSelectedTheme = useCallback((): BackgroundTheme => {
    const bgTheme = backgroundThemes.find(t => t.id === currentBackgroundSelection);
    return bgTheme || backgroundThemes[0]!;
  }, [currentBackgroundSelection]);

  const handleReset = useCallback(() => {
    setSelectedTheme(theme);
    setLightModeBackground(backgroundTheme);
    setDarkModeBackground(backgroundTheme);
  }, [theme, backgroundTheme]);

  // Check if changes were made
  const hasChanges = useMemo(() => {
    if (selectedTheme !== theme) return true;
    
    // Check if background would change based on current actual theme
    const currentResolvedTheme = theme === 'system' ? actualTheme : theme;
    const currentApplicableBackground = currentResolvedTheme === 'dark' ? darkModeBackground : lightModeBackground;
    
    return currentApplicableBackground !== backgroundTheme;
  }, [selectedTheme, theme, lightModeBackground, darkModeBackground, backgroundTheme, actualTheme]);

  return (
    <Dialog.Root open={isOpen} onOpenChange={onOpenChange}>
      <Dialog.Portal>
        <Dialog.Backdrop className="fixed inset-0 backdrop-blur-lg opacity-100 transition-all duration-150 data-[ending-style]:opacity-0 data-[starting-style]:opacity-0 z-[100000]" />
        <Dialog.Popup 
          ref={ref} 
          className="fixed top-1/2 left-1/2 -mt-8 w-[96vw] md:max-w-[700px] max-w-[calc(100vw-3rem)] -translate-x-1/2 -translate-y-1/2 rounded-lg lg bg-gradient-to-r from-[#f2f2f2]/80 via-[#e8e8e8]/70 to-[#eeeeee]/60 dark:from-[#101012]/80 dark:via-[#18181a]/70 dark:to-[#171719]/60 text-gray-900 dark:text-gray-100 outline-1 outline-black/10 dark:outline-white/10 transition-all duration-150 data-[ending-style]:scale-90 data-[ending-style]:opacity-0 data-[starting-style]:scale-90 data-[starting-style]:opacity-0 z-[100001] max-h-[90vh] overflow-hidden flex flex-col"
        >
          {/* Header */}
          <div className="flex-shrink-0 px-6 py-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <Palette className="w-5 h-5 text-black/70 dark:text-white/70" />
                <h2 className="text-lg font-redaction text-black/90 dark:text-white/90">
                  Theme & Background Settings
                </h2>
              </div>
              <Dialog.Close 
                className="rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:ring-2 focus:ring-ring focus:ring-offset-2 focus:outline-none disabled:pointer-events-none"
                // Prevent this from being auto-focused
                autoFocus={false}
              >
                <X className="h-4 w-4" />
                <span className="sr-only">Close</span>
              </Dialog.Close>
            </div>
          </div>

          {/* Content - Updated with ref and proper scrolling behavior */}
          <div 
            ref={scrollContainerRef}
            className="flex-1 px-6 py-6 overflow-y-auto custom-scrollbar space-y-4 focus:outline-none"
            tabIndex={0}
            role="region"
            aria-label="Theme settings content"
            style={{
              // Ensure smooth focus transition
              transition: 'all 0.1s ease-out'
            }}
            // Add mouse enter to focus for better UX
            onMouseEnter={() => {
              const scrollContainer = scrollContainerRef.current;
              if (scrollContainer) {
                scrollContainer.focus();
              }
            }}
          >
            {/* Theme Mode Selection */}
            <div className="space-y-4">
              <h3 className="text-sm font-redaction-normal font-medium text-black/80 dark:text-white/80">
                Theme Mode
              </h3>
              <div className="grid grid-cols-2 gap-3">
                {[
                  { value: 'light' as Theme, label: 'Light', icon: Sun },
                  { value: 'dark' as Theme, label: 'Dark', icon: Moon }
                ].map(({ value, label, icon: Icon }) => (
                  <button
                    key={value}
                    onClick={() => handleThemeChange(value)}
                    className={cn(
                      "p-4 rounded-xl border-2 transition-all duration-200 flex flex-col items-center gap-2 hover:scale-[1.02]",
                      selectedTheme === value
                        ? "border-blue-500 bg-blue-50 dark:bg-blue-950/30 shadow-md"
                        : "border-black/10 dark:border-white/10 bg-white/50 dark:bg-black/20 hover:border-black/20 dark:hover:border-white/20"
                    )}
                  >
                    <Icon className={cn(
                      "w-6 h-6",
                      selectedTheme === value ? "text-blue-600 dark:text-blue-400" : "text-black/60 dark:text-white/60"
                    )} />
                    <span className={cn(
                      "text-sm font-manrope_1 font-medium",
                      selectedTheme === value ? "text-blue-700 dark:text-blue-300" : "text-black/80 dark:text-white/80"
                    )}>
                      {label}
                    </span>
                  </button>
                ))}
              </div>
            </div>

            {/* Background Selection */}
            <div className="space-y-4">
              <h3 className="text-sm font-redaction-normal font-medium text-black/80 dark:text-white/80">
                Background Theme
                <span className="text-xs font-normal text-black/60 dark:text-white/60 ml-2">
                  ({previewMode === 'dark' ? 'Dark Mode' : 'Light Mode'})
                </span>
              </h3>
              <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
                {backgroundThemes.map((bgTheme) => {
                  const isSelected = currentBackgroundSelection === bgTheme.id;
                  
                  return (
                    <button
                      key={bgTheme.id}
                      onClick={() => handleBackgroundChange(bgTheme.id)}
                      className={cn(
                        "group relative p-3 rounded-xl border-2 transition-all duration-200 hover:scale-[1.02]",
                        isSelected
                          ? "border-blue-500 shadow-md"
                          : "border-black/10 dark:border-white/10 hover:border-black/20 dark:hover:border-white/20"
                      )}
                    >
                      {/* Preview */}
                      <div 
                        className="w-full h-20 rounded-lg border border-black/10 dark:border-white/10 mb-3 relative overflow-hidden"
                        style={getPreviewStyle(bgTheme)}
                      >
                        {isSelected && (
                          <div className="absolute inset-0 flex items-center justify-center">
                            <div className="bg-white/90 dark:bg-black/90 rounded-full p-1">
                              <Check className="w-4 h-4 text-blue-600 dark:text-blue-400" />
                            </div>
                          </div>
                        )}
                      </div>
                      
                      {/* Label */}
                      <p className={cn(
                        "text-xs font-manrope_1 font-medium text-center",
                        isSelected 
                          ? "text-blue-700 dark:text-blue-300" 
                          : "text-black/80 dark:text-white/80"
                      )}>
                        {bgTheme.name}
                      </p>
                    </button>
                  );
                })}
              </div>
            </div>

            {/* Preview Section */}
            <div className="space-y-4">
              <h3 className="text-sm font-redaction-normal font-medium text-black/80 dark:text-white/80">
                Preview
              </h3>
              <div className="rounded-xl border border-black/10 dark:border-white/10 overflow-hidden">
                <div 
                  className="h-32 relative"
                  style={previewMode === 'dark' ? getSelectedTheme().darkStyle : getSelectedTheme().lightStyle}
                >
                  <div className="absolute inset-0 flex items-center justify-center">
                    <div className="text-center">
                      <div className={cn(
                        "text-lg font-redaction-normal font-medium mb-1",
                        previewMode === 'dark' ? "text-white/90" : "text-black/90"
                      )}>
                        Preview
                      </div>
                      <div className={cn(
                        "text-sm font-manrope_1",
                        previewMode === 'dark' ? "text-white/70" : "text-black/70"
                      )}>
                        {getSelectedTheme().name} • {selectedTheme.charAt(0).toUpperCase() + selectedTheme.slice(1)}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Footer */}
          <div className="flex-shrink-0 px-6 py-4 border-t border-black/10 dark:border-white/10 bg-background">
            <div className="flex justify-between items-center">
              <div className="text-xs font-manrope_1 text-black/60 dark:text-white/60">
                Changes will be applied immediately
              </div>
              <div className="flex gap-3">
                <button
                  onClick={handleReset}
                  disabled={isLoading}
                  className="px-3 py-1.5 text-xs font-manrope_1 text-black/60 dark:text-white/60 hover:text-black/80 dark:hover:text-white/80 transition-colors"
                >
                  Reset
                </button>
                <button
                  onClick={handleSave}
                  disabled={isLoading}
                  className={cn(
                    "px-3 py-1.5 text-xs font-manrope_1 rounded-full transition-colors",
                    isLoading
                      ? "bg-black/60 dark:bg-white/60 text-white/40 dark:text-black/40 cursor-not-allowed"
                      : "bg-black/80 hover:bg-black dark:bg-white/90  dark:hover:bg-white text-white dark:text-black"
                  )}
                >
                  {isLoading ? 'Applying...' : 'Apply Changes'}
                </button>
              </div>
            </div>
          </div>
        </Dialog.Popup>
      </Dialog.Portal>
    </Dialog.Root>
  );
});