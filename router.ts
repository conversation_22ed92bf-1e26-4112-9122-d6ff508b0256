// router.ts
import { Hono } from "hono";
import { cors } from "hono/cors";
import { ronin, type Bindings, type Variables } from "blade-hono";
import { resend } from "./lib/resend";
import { WaitlistEmail as TeacherWaitlistEmail } from "./components/waitlist/teacher";
import { WaitlistEmail as SchoolWaitlistEmail } from "./components/waitlist/school";


// Import the unified auth instance
import { auth } from "./lib/auth";

// Define the Hono app with proper typing for RONIN and auth context
interface AppContext extends Variables {
  ronin: any;
  user: typeof auth.$Infer.Session.user | null;
  session: typeof auth.$Infer.Session.session | null;
}

const app = new Hono<{
  Bindings: Bindings;
  Variables: AppContext;
}>();

// Get environment variables - Blade 0.9.3+ handles server/client context automatically
const RONIN_TOKEN = process.env["BLADE_RONIN_TOKEN"] || process.env["RONIN_TOKEN"] || '';
const CORS_ORIGIN = process.env["BLADE_BETTER_AUTH_URL"] || 'http://localhost:3000';
const GOOGLE_PLACES_API_KEY = process.env["BLADE_GOOGLE_PLACES_API_KEY"] || '';

// Add RONIN middleware with explicit token since Blade doesn't set up c.env properly
if (RONIN_TOKEN) {
  app.use("*", ronin({
    token: RONIN_TOKEN
  }));
} else {
  console.error('RONIN_TOKEN not found in environment variables');
}

// Configure CORS specifically for auth routes (Better Auth needs this)
app.use(
  "/api/auth/*",
  cors({
    origin: CORS_ORIGIN,
    allowHeaders: ["Content-Type", "Authorization"],
    allowMethods: ["POST", "GET", "OPTIONS"],
    exposeHeaders: ["Content-Length"],
    maxAge: 600,
    credentials: true,
  })
);

// Configure CORS for other routes
app.use(
  "*",
  cors({
    origin: CORS_ORIGIN,
    allowHeaders: ["Content-Type", "Authorization"],
    allowMethods: ["POST", "GET", "OPTIONS"],
    exposeHeaders: ["Content-Length"],
    maxAge: 600,
    credentials: true,
  })
);

// Add Better Auth session middleware (skip for auth endpoints to avoid circular calls)
app.use("*", async (c, next) => {
  // Skip session middleware for auth endpoints to avoid circular dependency
  if (c.req.path.startsWith('/api/auth/')) {
    return next();
  }

  try {
    // Get session from Better Auth
    const session = await auth.api.getSession({
      headers: c.req.raw.headers
    });

    // Set session data in context for API routes
    c.set("user", session?.user || null);
    c.set("session", session?.session || null);
  } catch (error) {
    console.error('Session middleware error:', error);
    // Set null values if session retrieval fails
    c.set("user", null);
    c.set("session", null);
  }

  return next();
});

// Mount Better Auth API endpoints - single unified endpoint
app.on(["POST", "GET"], "/api/auth/*", async (c) => {
  const path = c.req.path;
  const method = c.req.method;
  
  console.log(`Better Auth request: ${method} ${path}`);
  
  // Handle waitlist validation BEFORE calling Better Auth for OTP sending
  if (path === "/api/auth/email-otp/send-verification-otp" && method === "POST") {
    try {
      // Clone the request to avoid locking the ReadableStream
      const clonedRequest = c.req.raw.clone();
      const body = await clonedRequest.json();
      const email = body?.email;

      if (email) {
        console.log(`Checking waitlist for email: ${email}`);

        // Get RONIN instance using the correct blade-hono syntax
        const ronin = c.var.ronin;

        // Check if user exists in waitlist
        const waitlistUser = await ronin.get.waitlist({
          with: { email: email }
        });

        if (!waitlistUser) {
          console.error(`User ${email} not found in waitlist - blocking OTP send`);
          return c.json({
            error: {
              message: "Your email is not on the waitlist. Please sign up first.",
              code: "WAITLIST_REQUIRED"
            }
          }, 400);
        }

        // Check if the user has been approved
        if (!waitlistUser.isApproved) {
          console.error(`User ${email} not approved in waitlist - blocking OTP send`);
          return c.json({
            error: {
              message: "Your account is awaiting approval. Please check back later.",
              code: "WAITLIST_NOT_APPROVED"
            }
          }, 400);
        }

        // NEW: Check if the user's role matches the context they're trying to sign in from
        // Get the selected role from the request header
        const selectedRole = c.req.header('X-Selected-Role') || 'teacher'; // default to teacher
        const expectedUserType = selectedRole; // Use the role selected in the UI

        console.log(`User attempting to sign in as: ${expectedUserType}, waitlist shows: ${waitlistUser.userType}`);

        // Check if waitlist userType matches expected role
        if (waitlistUser.userType !== expectedUserType) {
          const correctRole = waitlistUser.userType === 'school_admin' ? 'School Admin' : 'Teacher';

          console.error(`User ${email} is registered as ${waitlistUser.userType} but trying to sign in as ${expectedUserType}`);
          return c.json({
            error: {
              message: `This email is registered for ${correctRole} access.`,
              code: "WRONG_ROLE_SELECTED"
            }
          }, 400);
        }

        console.log(`User ${email} is approved in waitlist with correct role (${waitlistUser.userType}) - allowing OTP send`);
      }
    } catch (error) {
      console.error('Error checking waitlist before OTP send:', error);
      return c.json({
        error: {
          message: "Unable to verify waitlist status. Please try again later.",
          code: "WAITLIST_VALIDATION_ERROR"
        }
      }, 500);
    }
  }
  
  try {
    // Create a wrapped request that handles potential fetch issues
    const wrappedRequest = new Request(c.req.raw.url, {
      method: c.req.raw.method,
      headers: c.req.raw.headers,
      body: c.req.raw.body,
    });
    
    const response = await auth.handler(wrappedRequest);
    
    // Ensure we have a proper Response object
    if (!response) {
      console.error("Better Auth handler returned undefined response for:", path);
      return c.json({
        error: {
          message: "Authentication service error",
          code: "AUTH_SERVICE_ERROR"
        }
      }, 500);
    }

    // Check if response has proper structure before accessing headers
    if (typeof response !== 'object' || !('headers' in response)) {
      console.error("Better Auth response missing headers for:", path);
      return c.json({
        error: {
          message: "Authentication service error",
          code: "AUTH_SERVICE_ERROR"
        }
      }, 500);
    }

    // Log response details for debugging
    console.log(`Better Auth response status: ${response.status} for ${path}`);
    
    // Return the response directly - let Better Auth handle the formatting
    return response;
    
  } catch (error) {
    console.error(`Error in Better Auth handler for ${path}:`, error);
    
    // Handle specific waitlist validation errors first
    if (error instanceof Error) {
      if (error.message === 'WAITLIST_REQUIRED') {
        return c.json({
          error: {
            message: "Your email is not on the waitlist. Please sign up first.",
            code: "WAITLIST_REQUIRED"
          }
        }, 400);
      } else if (error.message === 'WAITLIST_NOT_APPROVED') {
        return c.json({
          error: {
            message: "Your account is awaiting approval. Please check back later.",
            code: "WAITLIST_NOT_APPROVED"
          }
        }, 400);
      } else if (error.message === 'WAITLIST_VALIDATION_ERROR') {
        return c.json({
          error: {
            message: "Unable to verify waitlist status. Please try again later.",
            code: "WAITLIST_VALIDATION_ERROR"
          }
        }, 500);
      } else if (error.message.includes('Email service')) {
        return c.json({
          error: {
            message: "Email service is temporarily unavailable. Please try again later.",
            code: "EMAIL_SERVICE_ERROR"
          }
        }, 500);
      } else if (error.message.includes('result.headers') || error.message.includes('undefined is not an object')) {
        // Handle the specific Better Auth headers error
        console.error('Better Auth headers error detected, likely after successful operation');
        // For OTP send operations, if we get here it might mean the OTP was sent but there's a response formatting issue
        if (path.includes('send-verification-otp')) {
          return c.json({
            error: {
              message: "OTP service temporarily unavailable. Please try again.",
              code: "OTP_SERVICE_ERROR"
            }
          }, 500);
        }
      }
    }
    
    return c.json({
      error: {
        message: "Authentication service error",
        code: "AUTH_SERVICE_ERROR",
        details: error instanceof Error ? error.message : 'Unknown error'
      }
    }, 500);
  }
});

// School search endpoint using Google Places API
app.post("/api/search-schools", async (c) => {
  console.log('School search endpoint called');
  
  try {
    const body = await c.req.json();
    console.log('Request body:', body);
    
    const { query } = body;

    if (!query || query.length < 3) {
      console.log('Query too short or missing:', query);
      return c.json({ schools: [] }, 200);
    }

    if (!GOOGLE_PLACES_API_KEY) {
      console.error("Google Places API key not configured");
      return c.json({ error: "School search service unavailable" }, 500);
    }

    console.log('Making request to Google Places API with query:', query);

    // Use Google Places API Text Search to find schools
    const searchUrl = new URL('https://maps.googleapis.com/maps/api/place/textsearch/json');
    searchUrl.searchParams.append('query', `${query} school`);
    searchUrl.searchParams.append('type', 'school');
    searchUrl.searchParams.append('key', GOOGLE_PLACES_API_KEY);

    console.log('Google Places URL:', searchUrl.toString().replace(GOOGLE_PLACES_API_KEY, 'BLADE_GOOGLE_PLACES_API_KEY'));

    const response = await fetch(searchUrl.toString());
    console.log('Google Places response status:', response.status);

    if (!response.ok) {
      console.error('Google Places API HTTP error:', response.status, response.statusText);
      const errorText = await response.text();
      console.error('Google Places error response:', errorText);
      return c.json({ error: "Failed to search schools", details: "API request failed" }, 500);
    }

    const data = await response.json();
    console.log('Google Places response status field:', data.status);
    console.log('Google Places results count:', data.results?.length || 0);

    if (data.status !== 'OK' && data.status !== 'ZERO_RESULTS') {
      console.error('Google Places API error:', data.status, data.error_message);
      return c.json({ 
        error: "Failed to search schools", 
        details: data.error_message || data.status 
      }, 500);
    }

    // Transform the results to our format
    const schools = data.results?.slice(0, 10).map((place: any) => ({
      place_id: place.place_id,
      name: place.name,
      formatted_address: place.formatted_address,
    })) || [];

    console.log('Returning schools:', schools.length);

    // Make sure we return a clean JSON response
    const responseData = { schools };
    
    // Set proper headers to ensure clean JSON response
    c.header('Content-Type', 'application/json');
    return c.json(responseData, 200);

  } catch (error) {
    console.error("School search error:", error);
    
    // Return a proper JSON error response with clean headers
    c.header('Content-Type', 'application/json');
    return c.json({ 
      error: "School search failed",
      details: error instanceof Error ? error.message : 'Unknown error'
    }, 500);
  }
});

// Get registered schools endpoint
app.get("/api/registered-schools", async (c) => {
  try {
    // Get RONIN instance from middleware
    const ronin = c.var.ronin;

    if (!ronin) {
      console.error('RONIN instance not available');
      c.header('Content-Type', 'application/json');
      return c.json({
        error: "Database service unavailable",
        registeredPlaceIds: [] // Return empty array as fallback
      }, 500);
    }

    // Get all school admins from waitlist to check which schools are already registered
    let registeredSchools = [];
    try {
      // Query for school admins specifically
      const schoolAdminQuery = await ronin.get.waitlist({
        with: {
          userType: 'school_admin'
        }
      });

      // Handle the case where RONIN returns a single object vs array
      if (schoolAdminQuery) {
        registeredSchools = Array.isArray(schoolAdminQuery) ? schoolAdminQuery : [schoolAdminQuery];
      } else {
        // No school admins found, which is fine
        registeredSchools = [];
      }
    } catch (queryError) {
      console.error('Query error:', queryError);
      registeredSchools = [];
    }

    // Extract place IDs of registered schools
    const registeredPlaceIds = registeredSchools
      .filter((entry: any) => entry && entry.schoolPlaceId)
      .map((entry: any) => entry.schoolPlaceId);

    c.header('Content-Type', 'application/json');
    return c.json({ registeredPlaceIds }, 200);

  } catch (error) {
    console.error("Error fetching registered schools:", error);
    c.header('Content-Type', 'application/json');
    return c.json({
      error: "Failed to fetch registered schools",
      details: error instanceof Error ? error.message : 'Unknown error',
      registeredPlaceIds: [] // Return empty array as fallback
    }, 200); // Return 200 with empty array instead of 500 error
  }
});

// Enhanced waitlist signup endpoint with school support
app.post("/api/join-waitlist", async (c) => {
  try {
    const { email, userType, name, school } = await c.req.json();

    if (!email || !userType || !name) {
      c.header('Content-Type', 'application/json');
      return c.json({ error: "Email, user type, and name are required" }, 400);
    }

    // Validate user type
    if (!['teacher', 'school_admin'].includes(userType)) {
      c.header('Content-Type', 'application/json');
      return c.json({ error: "Invalid user type" }, 400);
    }

    // For school admins, require school information
    if (userType === 'school_admin' && !school) {
      c.header('Content-Type', 'application/json');
      return c.json({ error: "School information is required for school administrators" }, 400);
    }

    // Get RONIN instance using the correct blade-hono syntax
    const ronin = c.var.ronin;
    
    // Check if email already exists in waitlist (regardless of userType)
    try {
      const existingEmailEntry = await ronin.get.waitlist({
        with: {
          email: email
        }
      });

      if (existingEmailEntry) {
        c.header('Content-Type', 'application/json');
        return c.json({ error: "Email already exists" }, 400);
      }
    } catch (error) {
      console.error('Error checking existing email:', error);
      // Continue with the signup process - the unique constraint will catch duplicates
    }

    // For school admins, also check if the school is already registered
    if (userType === 'school_admin' && school) {
      try {
        const existingSchoolEntry = await ronin.get.waitlist({
          with: {
            schoolPlaceId: school.place_id,
            userType: 'school_admin'
          }
        });

        // Handle both single object and array responses from RONIN
        if (existingSchoolEntry) {
          const schoolEntries = Array.isArray(existingSchoolEntry) ? existingSchoolEntry : [existingSchoolEntry];
          if (schoolEntries.length > 0) {
            c.header('Content-Type', 'application/json');
            return c.json({ error: "School already registered" }, 400);
          }
        }
      } catch (error) {
        console.error('Error checking existing school:', error);
        // Continue with the signup process - the unique constraint will catch duplicates
      }
    }

    // Prepare waitlist data
    const waitlistData: any = {
      email,
      userType: userType,
      name: name.trim(),
      createdAt: new Date(),
    };

    // Add school information for school admins
    if (userType === 'school_admin' && school) {
      waitlistData.schoolPlaceId = school.place_id;
      waitlistData.schoolName = school.name;
      waitlistData.schoolAddress = school.formatted_address;
    }

    // Add to waitlist database
    try {
      await ronin.add.waitlist.with(waitlistData);
    } catch (error) {
      console.error('Error adding to waitlist:', error);
      // Check if it's a unique constraint violation
      if (error instanceof Error && (
        error.message.includes('unique') ||
        error.message.includes('duplicate') ||
        error.message.includes('UNIQUE constraint failed')
      )) {
        c.header('Content-Type', 'application/json');
        return c.json({ error: "Email already exists" }, 400);
      }
      // Re-throw other errors
      throw error;
    }

    // Choose the appropriate email template
    const EmailTemplate = userType === 'teacher' ? TeacherWaitlistEmail : SchoolWaitlistEmail;

    // Send confirmation email
    const emailResult = await resend.emails.send({
      from: "Penned <<EMAIL>>",
      to: email,
      subject: `Welcome to the Penned ${userType === 'teacher' ? 'Teacher' : 'School'} Waitlist`,
      react: EmailTemplate({
        name: name.trim(),
        school: userType === 'school_admin' ? school : undefined
      }) as any,
    });

    if (emailResult.error) {
      console.error("Error sending email:", emailResult.error);
      c.header('Content-Type', 'application/json');
      return c.json({ error: "Failed to send confirmation email" }, 500);
    }

    c.header('Content-Type', 'application/json');
    return c.json({
      message: "Successfully joined waitlist",
      email: email,
      userType: userType
    }, 200);

  } catch (error) {
    console.error("Waitlist signup error:", error);
    
    c.header('Content-Type', 'application/json');
    return c.json({
      error: "Waitlist signup failed",
      details: error instanceof Error ? error.message : 'Unknown error'
    }, 500);
  }
});

// Update user endpoint for post-OTP user updates
app.post("/api/update-user", async (c) => {
  try {
    const { userId, updates } = await c.req.json();

    if (!userId || !updates) {
      c.header('Content-Type', 'application/json');
      return c.json({ error: "Missing userId or updates" }, 400);
    }

    console.log('Updating user:', userId, 'with:', updates);

    // Get RONIN instance
    const ronin = c.var.ronin;

    if (!ronin) {
      console.error('RONIN instance not available');
      c.header('Content-Type', 'application/json');
      return c.json({ error: "Database service unavailable" }, 500);
    }

    // Update user in database
    await ronin.set.user({
      with: { id: userId },
      to: updates
    });

    console.log('User updated successfully:', userId);

    c.header('Content-Type', 'application/json');
    return c.json({ success: true });

  } catch (error) {
    console.error('Error updating user:', error);
    c.header('Content-Type', 'application/json');
    return c.json({ error: "Failed to update user" }, 500);
  }
})





// Check if user exists endpoint
app.post("/api/check-user-exists", async (c) => {
  try {
    // Get the current session to verify the user is authenticated
    const session = await auth.api.getSession({
      headers: c.req.raw.headers
    });

    if (!session || !session.user) {
      c.header('Content-Type', 'application/json');
      return c.json({ error: 'Unauthorized' }, 401);
    }

    // Check if the user is a teacher or school_admin
    if (session.user.role !== 'teacher' && session.user.role !== 'school_admin') {
      c.header('Content-Type', 'application/json');
      return c.json({ error: 'Insufficient permissions' }, 403);
    }

    // Get the request body
    const body = await c.req.json();
    const { email } = body;

    if (!email) {
      c.header('Content-Type', 'application/json');
      return c.json({ error: 'Email is required' }, 400);
    }

    console.log('Checking if user exists:', email);

    // Check if user exists using RONIN
    const ronin = c.var.ronin;
    const existingUsers = await ronin.get.users({
      with: { email: email.toLowerCase().trim() }
    });

    const exists = existingUsers && existingUsers.length > 0;
    const user = exists ? existingUsers[0] : null;

    console.log('User exists check result:', { email, exists, userId: user?.id });

    c.header('Content-Type', 'application/json');
    return c.json({ exists, user });

  } catch (error) {
    console.error('Error checking if user exists:', error);
    c.header('Content-Type', 'application/json');
    return c.json({ error: 'Internal server error' }, 500);
  }
});

// Create class endpoint (bypasses trigger issues)
app.post("/api/create-class", async (c) => {
  try {
    // Get the current session to verify the user is a teacher
    const session = await auth.api.getSession({
      headers: c.req.raw.headers
    });

    if (!session || !session.user) {
      c.header('Content-Type', 'application/json');
      return c.json({ error: 'Unauthorized' }, 401);
    }

    // Check if the user is a teacher or school_admin
    if (session.user.role !== 'teacher' && session.user.role !== 'school_admin') {
      c.header('Content-Type', 'application/json');
      return c.json({ error: 'Insufficient permissions' }, 403);
    }

    // Get the request body
    const body = await c.req.json();
    const { name, description, gradeLevel, maxCapacity } = body;

    console.log('Creating class via API:', { name, description, gradeLevel, maxCapacity, teacherId: session.user.id });

    // Get RONIN instance
    const ronin = c.var.ronin;

    // Get the Teacher record ID (not the User ID) - needed for class creation
    let teacherRecordId;
    try {
      const teacherRecord = await ronin.get.teacher({
        where: { userId: session.user.id }
      });
      teacherRecordId = teacherRecord.id;
      console.log('📝 Found teacher record:', { teacherRecordId, teacherUserId: session.user.id });
    } catch (teacherError) {
      console.error('❌ Failed to find teacher record:', teacherError);
      c.header('Content-Type', 'application/json');
      return c.json({ error: 'Teacher record not found' }, 404);
    }

    // Auto-generate slug from class name
    const slug = name
      .toLowerCase()
      .replace(/\s+/g, '-')
      .replace(/[^a-z0-9-]/g, '-')
      .replace(/-+/g, '-')
      .replace(/^-|-$/g, '') || 'class';

    // Create the class using RONIN directly
    try {
      console.log('📝 Creating class record:', {
        name,
        description: description || `${name} class`,
        teacherId: teacherRecordId,
        gradeLevel: gradeLevel || '',
        maxCapacity: maxCapacity || 30,
        slug
      });

      const classRecord = await ronin.add.class({
        with: {
          name: name,
          description: description || `${name} class`,
          teacherId: teacherRecordId, // Use Teacher record ID, not User ID
          gradeLevel: gradeLevel || '',
          maxCapacity: maxCapacity || 30,
          currentEnrollment: 0,
          isActive: true,
          slug: slug,
          createdAt: new Date(),
          updatedAt: new Date()
        }
      });

      console.log('✅ Class created successfully:', classRecord);

      c.header('Content-Type', 'application/json');
      return c.json({
        success: true,
        class: {
          id: classRecord.id,
          name: classRecord.name,
          description: classRecord.description,
          teacherId: classRecord.teacherId,
          gradeLevel: classRecord.gradeLevel,
          maxCapacity: classRecord.maxCapacity,
          currentEnrollment: classRecord.currentEnrollment,
          isActive: classRecord.isActive,
          slug: classRecord.slug,
          createdAt: classRecord.createdAt,
          updatedAt: classRecord.updatedAt
        }
      });

    } catch (error) {
      console.error('❌ Error creating class:', error);
      c.header('Content-Type', 'application/json');
      return c.json({ error: 'Failed to create class' }, 500);
    }

  } catch (error) {
    console.error('Error in create-class endpoint:', error);
    c.header('Content-Type', 'application/json');
    return c.json({ error: 'Internal server error' }, 500);
  }
});

// Create student endpoint (bypasses admin plugin issues)
app.post("/api/create-student", async (c) => {
  try {
    // Get the current session to verify the user is a teacher
    const session = await auth.api.getSession({
      headers: c.req.raw.headers
    });

    if (!session || !session.user) {
      c.header('Content-Type', 'application/json');
      return c.json({ error: 'Unauthorized' }, 401);
    }

    // Check if the user is a teacher or school_admin
    if (session.user.role !== 'teacher' && session.user.role !== 'school_admin') {
      c.header('Content-Type', 'application/json');
      return c.json({ error: 'Insufficient permissions' }, 403);
    }

    // Get the request body
    const body = await c.req.json();
    const { email, password, name, username, teacherId, grade, classes } = body;

    console.log('Creating student via API:', { email, name, username, teacherId, grade, classes });

    // Create the user using Better Auth's server-side API
    const result = await auth.api.signUpEmail({
      body: {
        email,
        password,
        name
      }
    });

    if (!result || !result.user) {
      c.header('Content-Type', 'application/json');
      return c.json({ error: 'Failed to create user account' }, 500);
    }

    const userId = result.user.id;
    console.log('User account created, updating with student data:', userId);

    // Generate a slug for the student based on their name
    const generateSlug = (name: string) => {
      return name
        .toLowerCase()
        .replace(/[^a-z0-9\s-]/g, '') // Remove special characters
        .replace(/\s+/g, '-') // Replace spaces with hyphens
        .replace(/-+/g, '-') // Replace multiple hyphens with single hyphen
        .trim();
    };

    const studentSlug = generateSlug(name);

    // Update the user with student-specific fields using RONIN
    const ronin = c.var.ronin;
    try {
      console.log('📝 Updating user with student data:', {
        userId,
        role: 'student',
        email,
        name,
        username,
        slug: studentSlug,
        grade: grade || null
      });

      await ronin.set.user({
        with: { id: userId },
        to: {
          role: 'student',
          email: email, // Include email for trigger
          name: name,   // Include name for trigger
          username: username,
          slug: studentSlug, // Add the generated slug
          grade: grade || null,
          isActive: true,
          emailVerified: false
        }
      });

      console.log('✅ User updated with student data:', userId);

    } catch (updateError) {
      console.error('❌ Failed to update user with student data:', updateError);
      console.error('❌ Update error details:', {
        message: updateError instanceof Error ? updateError.message : 'Unknown error',
        code: (updateError as any)?.code || 'NO_CODE',
        userId: userId,
        studentSlug: studentSlug
      });
      throw updateError; // Re-throw since this is critical
    }

    // Get the Teacher record ID (not the User ID) - needed for StudentTeacher relationship
    let teacherRecordId;
    try {
      const teacherRecord = await ronin.get.teacher({
        where: { userId: teacherId }
      });
      teacherRecordId = teacherRecord.id;
      console.log('📝 Found teacher record:', { teacherRecordId, teacherUserId: teacherId });
    } catch (teacherError) {
      console.error('❌ Failed to find teacher record:', teacherError);
      throw new Error('Teacher record not found');
    }

    // Create the Student record manually (since set.user doesn't trigger afterAdd)
    try {
      console.log('📝 Creating Student record for user:', userId);
      const studentRecord = await ronin.add.student({
        with: {
          userId: userId,
          grade: grade || null, // Use null instead of empty string
          createdAt: new Date(),
          updatedAt: new Date()
        }
      });
      console.log('✅ Student record created successfully:', studentRecord);

      // Now create StudentTeacher relationship using the actual record IDs
      try {
        console.log('📝 Creating StudentTeacher relationship:', {
          studentRecordId: studentRecord.id,
          teacherRecordId: teacherRecordId
        });
        await ronin.add.studentTeacher({
          with: {
            studentId: studentRecord.id,
            teacherId: teacherRecordId,
            assignedAt: new Date(),
            status: 'active'
          }
        });
        console.log('✅ StudentTeacher relationship created successfully');
      } catch (relationshipError) {
        console.error('❌ Failed to create StudentTeacher relationship:', relationshipError);
        // Don't fail the entire operation if relationship creation fails
      }

    } catch (studentError) {
      console.error('❌ Failed to create Student record:', studentError);
      console.error('❌ Student error details:', {
        message: studentError instanceof Error ? studentError.message : 'Unknown error',
        code: (studentError as any)?.code || 'NO_CODE',
        userId: userId,
        grade: grade
      });
      // Don't fail the entire operation if Student record creation fails
      // But log the detailed error for debugging
    }

    console.log('Student created successfully:', userId);

    // Send the invitation email directly from here with the password
    try {
      console.log('📧 Sending student invitation email...');

      // Import email dependencies
      const { resend, validateEmailConfig } = await import('./lib/resend');
      const { StudentInvitationEmail } = await import('./components/emails/student-invitation');
      const { render } = await import('@react-email/render');

      // Validate email configuration
      const emailValidation = validateEmailConfig();
      if (!emailValidation.isValid) {
        console.error('❌ Email configuration error:', emailValidation.error);
        throw new Error(`Email service not configured: ${emailValidation.error}`);
      }

      // Generate the login URL
      const baseUrl = process.env['BLADE_BETTER_AUTH_URL'] || 'http://localhost:3000';
      const loginUrl = `${baseUrl}/login?role=student`;

      // Render the email template
      const emailComponent = StudentInvitationEmail({
        studentName: name,
        teacherName: 'Your Teacher', // Placeholder for now
        username: username,
        email: email,
        classes: [], // TODO: Add classes when implemented
        loginUrl,
        password: password // Use the actual generated password
      });

      const emailHtml = await render(emailComponent as any);

      // Send the email
      const result = await resend.emails.send({
        from: 'Penned <<EMAIL>>',
        to: email,
        subject: `Welcome to Penned - Your Student Account is Ready!`,
        html: emailHtml,
      });

      if (result.error) {
        console.error('❌ Resend API error:', result.error);
        throw new Error(`Failed to send email: ${result.error.message}`);
      }

      console.log('✅ Student invitation email sent successfully to:', email);
      console.log('📧 Email ID:', result.data?.id);
    } catch (emailError) {
      console.error('❌ Failed to send student invitation email:', emailError);
      // Don't fail the student creation if email fails
    }

    c.header('Content-Type', 'application/json');
    return c.json({
      success: true,
      user: {
        id: userId,
        email,
        name,
        username,
        role: 'student'
      }
    });

  } catch (error) {
    console.error('Error creating student:', error);
    c.header('Content-Type', 'application/json');
    return c.json({ error: 'Internal server error' }, 500);
  }
});

// Get waitlist user data endpoint
app.post("/api/get-waitlist-user", async (c) => {
  try {
    const { email } = await c.req.json();

    if (!email) {
      c.header('Content-Type', 'application/json');
      return c.json({ error: "Email is required" }, 400);
    }

    // Get RONIN instance
    const ronin = c.var.ronin;

    // Get waitlist user data
    const waitlistUser = await ronin.get.waitlist({
      with: { email: email }
    });

    if (!waitlistUser) {
      c.header('Content-Type', 'application/json');
      return c.json({ error: "User not found in waitlist" }, 404);
    }

    c.header('Content-Type', 'application/json');
    return c.json({
      name: waitlistUser.name,
      userType: waitlistUser.userType,
      email: waitlistUser.email
    });

  } catch (error) {
    console.error("Get waitlist user error:", error);

    c.header('Content-Type', 'application/json');
    return c.json({
      error: "Failed to get waitlist user data",
      details: error instanceof Error ? error.message : 'Unknown error'
    }, 500);
  }
});

// Ensure role-specific record exists (Teacher/Student/SchoolAdmin)
app.post('/api/ensure-role-record', async (c) => {
  try {
    const body = await c.req.json();
    const { userId, role, userData } = body;

    if (!userId || !role) {
      c.header('Content-Type', 'application/json');
      return c.json({ error: 'Missing userId or role' }, 400);
    }

    console.log('🔍 Checking if role-specific record exists for:', { userId, role });

    let recordExists = false;
    let createRecord = null;

    // Check if role-specific record exists and create if needed
    switch (role) {
      case 'teacher':
        try {
          const existingTeacher = await c.var.ronin.get.teacher({
            with: { userId: userId }
          });
          recordExists = !!existingTeacher;

          if (!recordExists) {
            createRecord = {
              userId: userId,
              bio: '',
              isVerified: false,
              isIndependent: true,
              createdAt: new Date(),
              updatedAt: new Date()
            };
          }
        } catch (error) {
          console.log('Teacher record not found, will create');
          recordExists = false;
        }
        break;

      case 'student':
        try {
          const existingStudent = await c.var.ronin.get.student({
            with: { userId: userId }
          });
          recordExists = !!existingStudent;

          if (!recordExists) {
            createRecord = {
              userId: userId,
              grade: '',
              createdAt: new Date(),
              updatedAt: new Date()
            };
          }
        } catch (error) {
          console.log('Student record not found, will create');
          recordExists = false;
        }
        break;

      case 'school_admin':
        try {
          const existingSchoolAdmin = await c.var.ronin.get.schoolAdmin({
            with: { userId: userId }
          });
          recordExists = !!existingSchoolAdmin;

          if (!recordExists && userData?.schoolId) {
            createRecord = {
              userId: userId,
              schoolId: userData.schoolId,
              createdAt: new Date(),
              updatedAt: new Date()
            };
          }
        } catch (error) {
          console.log('SchoolAdmin record not found, will create');
          recordExists = false;
        }
        break;

      default:
        c.header('Content-Type', 'application/json');
        return c.json({ error: 'Invalid role' }, 400);
    }

    if (recordExists) {
      console.log('✅ Role-specific record already exists for:', role);
      c.header('Content-Type', 'application/json');
      return c.json({
        success: true,
        message: `${role} record already exists`,
        created: false
      });
    }

    if (createRecord) {
      // Create the role-specific record
      switch (role) {
        case 'teacher':
          await c.var.ronin.add.teacher({ with: createRecord });
          console.log('✅ Created Teacher record for user:', userId);
          break;
        case 'student':
          await c.var.ronin.add.student({ with: createRecord });
          console.log('✅ Created Student record for user:', userId);
          break;
        case 'school_admin':
          await c.var.ronin.add.schoolAdmin({ with: createRecord });
          console.log('✅ Created SchoolAdmin record for user:', userId);
          break;
      }

      c.header('Content-Type', 'application/json');
      return c.json({
        success: true,
        message: `${role} record created successfully`,
        created: true
      });
    } else {
      console.warn('⚠️ Could not create record for role:', role, '(missing required data)');
      c.header('Content-Type', 'application/json');
      return c.json({
        error: `Cannot create ${role} record - missing required data`
      }, 400);
    }

  } catch (error) {
    console.error('❌ Error in ensure-role-record:', error);
    c.header('Content-Type', 'application/json');
    return c.json({ error: 'Internal server error' }, 500);
  }
});

// Avatar upload endpoint - handles file upload to RONIN storage
app.post('/api/upload-avatar', async (c) => {
  try {
    console.log('🖼️ Avatar request received...');

    // Check if this is a JSON request (for removal) or form data (for upload)
    const contentType = c.req.header('content-type');

    if (contentType?.includes('application/json')) {
      // Handle avatar removal
      const body = await c.req.json();
      const { userId, removeAvatar } = body;

      if (!userId) {
        return c.json({ error: 'No user ID provided' }, 400);
      }

      if (removeAvatar) {
        console.log('🗑️ Removing avatar for user:', userId);

        // Remove the avatar by setting image to null
        const result = await c.var.ronin.set.user({
          with: { id: userId },
          to: { image: null }
        });

        console.log('✅ Avatar removed successfully:', result);

        return c.json({
          success: true,
          message: 'Avatar removed successfully',
          user: result
        });
      }
    } else {
      // Handle file upload
      const body = await c.req.parseBody();
      const file = body['file'] as File;
      const userId = body['userId'] as string;

      if (!file) {
        return c.json({ error: 'No file provided' }, 400);
      }

      if (!userId) {
        return c.json({ error: 'No user ID provided' }, 400);
      }

      console.log('📁 Avatar file received:', {
        name: file.name,
        size: file.size,
        type: file.type,
        userId: userId
      });

      // Validate file type and size
      if (!file.type.startsWith('image/')) {
        return c.json({ error: 'File must be an image' }, 400);
      }

      if (file.size > 5 * 1024 * 1024) {
        return c.json({ error: 'File must be smaller than 5MB' }, 400);
      }

      // Update the user record with the new image using RONIN directly
      const result = await c.var.ronin.set.user({
        with: { id: userId },
        to: { image: file }
      });

      console.log('✅ Avatar uploaded successfully to RONIN storage:', result);

      return c.json({
        success: true,
        message: 'Avatar uploaded successfully',
        user: result
      });
    }

    return c.json({ error: 'Invalid request' }, 400);

  } catch (error) {
    console.error('❌ Avatar operation failed:', error);
    return c.json({
      error: 'Operation failed',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, 500);
  }
});

export default app;

