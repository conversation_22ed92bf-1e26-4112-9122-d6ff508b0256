// triggers/account.ts
import type { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, RemoveTrigger } from 'blade/types';

// During triggers - required by Blade for write operations
export const during = {
  add: (...args: any[]) => {
    const [query, _multiple, _options] = args;
    const typedQuery = query as any;

    // Ensure query.with exists
    if (!typedQuery.with) {
      return typedQuery;
    }

    // Handle both single object and array cases
    const processAccountData = (accountData: any) => {
      console.log('Account during.add trigger - processing data:', accountData);

      // Set default values if needed
      accountData.attempts = accountData.attempts || 0;

      console.log('Account during.add trigger - processed data:', accountData);
      return accountData;
    };

    // Handle array of accounts
    if (Array.isArray(typedQuery.with)) {
      typedQuery.with = typedQuery.with.map(processAccountData);
    } else {
      // Handle single account
      typedQuery.with = processAccountData(typedQuery.with);
    }

    return typedQuery;
  },

  set: (...args: any[]) => {
    const [query, _multiple, _options] = args;
    const typedQuery = query as any;

    // Ensure query.to exists
    if (!typedQuery.to) {
      return typedQuery;
    }

    console.log('Account during.set trigger - processed data:', typedQuery.to);
    return typedQuery;
  },

  remove: (...args: any[]) => {
    const [query, _multiple, _options] = args;
    const typedQuery = query as any;

    // Add any validation or cleanup logic for deletions
    console.log('Account during.remove trigger called with query:', typedQuery);
    return typedQuery;
  }
};

// Trigger for creating accounts
export const add: AddTrigger = (...args) => {
  const [query, _multiple, _options] = args;
  const typedQuery = query as any;

  // Ensure query.with exists
  if (!typedQuery.with) {
    return typedQuery;
  }

  // Handle both single object and array cases
  const processAccountData = (accountData: any) => {
    console.log('Account creation trigger - processing data:', accountData);

    // Set default values if needed
    accountData.attempts = accountData.attempts || 0;

    console.log('Account trigger - processed account data:', accountData);
    return accountData;
  };

  // Handle array of accounts
  if (Array.isArray(typedQuery.with)) {
    typedQuery.with = typedQuery.with.map(processAccountData);
  } else {
    // Handle single account
    typedQuery.with = processAccountData(typedQuery.with);
  }

  return typedQuery;
};

// Trigger for updating accounts
export const set: SetTrigger = (...args) => {
  const [query, _multiple, _options] = args;
  const typedQuery = query as any;

  // Ensure query.to exists
  if (!typedQuery.to) {
    return typedQuery;
  }

  console.log('Account update trigger - processed data:', typedQuery.to);
  return typedQuery;
};

// Trigger for getting accounts (can be used for access control)
export const get: GetTrigger = (...args) => {
  const [query, _multiple, _options] = args;
  const typedQuery = query as any;

  // Add any access control logic here if needed
  return typedQuery;
};

// Trigger for removing accounts
export const remove: RemoveTrigger = (...args) => {
  const [query, _multiple, _options] = args;
  const typedQuery = query as any;

  // Add any validation or cleanup logic for deletions
  console.log('Account removal trigger called with query:', typedQuery);
  return typedQuery;
};
