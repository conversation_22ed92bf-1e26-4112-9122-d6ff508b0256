// triggers/otp.ts
import type { <PERSON>d<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>rigger, RemoveTrigger } from 'blade/types';

// During triggers - required by Blade for write operations
export const during = {
  add: (...args: any[]) => {
    const [query, _multiple, _options] = args;
    const typedQuery = query as any;

    // Ensure query.with exists
    if (!typedQuery.with) {
      return typedQuery;
    }

    // Handle both single object and array cases
    const processOtpData = (otpData: any) => {
      console.log('OTP during.add trigger - processing data:', otpData);

      // Set default values
      otpData.attempts = otpData.attempts || 0;
      otpData.createdAt = otpData.createdAt || new Date();

      // Set default expiration if not provided (10 minutes from now)
      if (!otpData.expiresAt) {
        const expiresAt = new Date();
        expiresAt.setMinutes(expiresAt.getMinutes() + 10);
        otpData.expiresAt = expiresAt;
      }

      console.log('OTP during.add trigger - processed data:', otpData);
      return otpData;
    };

    // Handle array of OTPs
    if (Array.isArray(typedQuery.with)) {
      typedQuery.with = typedQuery.with.map(processOtpData);
    } else {
      // Handle single OTP
      typedQuery.with = processOtpData(typedQuery.with);
    }

    return typedQuery;
  },

  set: (...args: any[]) => {
    const [query, _multiple, _options] = args;
    const typedQuery = query as any;

    // Ensure query.to exists
    if (!typedQuery.to) {
      return typedQuery;
    }

    console.log('OTP during.set trigger - processed data:', typedQuery.to);
    return typedQuery;
  },

  remove: (...args: any[]) => {
    const [query, _multiple, _options] = args;
    const typedQuery = query as any;

    // Add any validation or cleanup logic for deletions
    console.log('OTP during.remove trigger called with query:', typedQuery);
    return typedQuery;
  }
};

// Trigger for creating OTPs
export const add: AddTrigger = (...args) => {
  const [query, _multiple, _options] = args;
  const typedQuery = query as any;

  // Ensure query.with exists
  if (!typedQuery.with) {
    return typedQuery;
  }

  // Handle both single object and array cases
  const processOtpData = (otpData: any) => {
    console.log('OTP creation trigger - processing data:', otpData);

    // Set default values
    otpData.attempts = otpData.attempts || 0;
    otpData.createdAt = otpData.createdAt || new Date();

    // Set default expiration if not provided (10 minutes from now)
    if (!otpData.expiresAt) {
      const expiresAt = new Date();
      expiresAt.setMinutes(expiresAt.getMinutes() + 10);
      otpData.expiresAt = expiresAt;
    }

    console.log('OTP trigger - processed OTP data:', otpData);
    return otpData;
  };

  // Handle array of OTPs
  if (Array.isArray(typedQuery.with)) {
    typedQuery.with = typedQuery.with.map(processOtpData);
  } else {
    // Handle single OTP
    typedQuery.with = processOtpData(typedQuery.with);
  }

  return typedQuery;
};

// Trigger for updating OTPs
export const set: SetTrigger = (...args) => {
  const [query, _multiple, _options] = args;
  const typedQuery = query as any;

  // Ensure query.to exists
  if (!typedQuery.to) {
    return typedQuery;
  }

  console.log('OTP update trigger - processed data:', typedQuery.to);
  return typedQuery;
};

// Trigger for getting OTPs (can be used for access control)
export const get: GetTrigger = (...args) => {
  const [query, _multiple, _options] = args;
  const typedQuery = query as any;

  // Add any access control logic here if needed
  return typedQuery;
};

// Trigger for removing OTPs
export const remove: RemoveTrigger = (...args) => {
  const [query, _multiple, _options] = args;
  const typedQuery = query as any;

  // Add any validation or cleanup logic for deletions
  console.log('OTP removal trigger called with query:', typedQuery);
  return typedQuery;
};
