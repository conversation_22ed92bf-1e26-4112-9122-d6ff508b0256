// triggers/class.ts
import type { Add<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ger, GetTrigger, RemoveTrigger } from 'blade/types';

// Trigger for creating classes
export const add: AddTrigger = async (...args) => {
  const [query, _multiple, options] = args;
  const typedQuery = query as any;
  const typedOptions = options as any;

  // Ensure query.with exists
  if (!typedQuery.with) {
    return typedQuery;
  }

  // Handle both single object and array cases
  const processClassData = async (classData: any) => {
    // Convert teacherId from User ID to Teacher record ID
    if (classData.teacherId) {
      try {
        const teacherRecord = await typedOptions.client.get.teacher({
          where: { userId: classData.teacherId }
        });

        if (teacherRecord) {
          classData.teacherId = teacherRecord.id;
          console.log('✅ Converted User ID to Teacher record ID:', {
            userId: classData.teacherId,
            teacherRecordId: teacherRecord.id
          });
        } else {
          console.error('❌ Teacher record not found for user:', classData.teacherId);
          throw new Error('Teacher record not found for user');
        }
      } catch (error) {
        console.error('❌ Failed to find teacher record:', error);
        throw new Error('Invalid teacher ID');
      }
    }

    // Auto-generate slug if not provided
    if (!classData.slug) {
      const baseName = classData.name || 'class';
      classData.slug = baseName
        .toLowerCase()
        .replace(/\s+/g, '-')
        .replace(/[^a-z0-9-]/g, '-')
        .replace(/-+/g, '-')
        .replace(/^-|-$/g, '') || 'class';
    }

    // Set default values
    classData.isActive = classData.isActive ?? true;
    classData.currentEnrollment = classData.currentEnrollment ?? 0;
    classData.maxCapacity = classData.maxCapacity ?? 30;
    classData.createdAt = classData.createdAt || new Date();
    classData.updatedAt = classData.updatedAt || new Date();

    console.log('Class trigger - processed class data:', classData);
    return classData;
  };

  // Handle array of classes
  if (Array.isArray(typedQuery.with)) {
    typedQuery.with = await Promise.all(typedQuery.with.map(processClassData));
  } else {
    // Handle single class
    typedQuery.with = await processClassData(typedQuery.with);
  }

  return typedQuery;
};

// Trigger for updating classes
export const set: SetTrigger = (...args) => {
  const [query, _multiple, _options] = args;
  const typedQuery = query as any;

  // Ensure query.to exists
  if (!typedQuery.to) {
    return typedQuery;
  }

  // Update timestamp
  typedQuery.to.updatedAt = new Date();

  // Update slug if name is being changed and slug is not already provided
  if (typedQuery.to.name && !typedQuery.to.slug) {
    typedQuery.to.slug = typedQuery.to.name
      .toLowerCase()
      .replace(/\s+/g, '-')
      .replace(/[^a-z0-9-]/g, '-')
      .replace(/-+/g, '-')
      .replace(/^-|-$/g, '') || 'class';
  }

  console.log('Class update trigger - processed data:', typedQuery.to);
  return typedQuery;
};

// Trigger for getting classes (can be used for access control)
export const get: GetTrigger = (...args) => {
  const [query, _multiple, _options] = args;
  const typedQuery = query as any;

  // Add any access control logic here if needed
  return typedQuery;
};

// Trigger for removing classes
export const remove: RemoveTrigger = (...args) => {
  const [query, _multiple, _options] = args;
  const typedQuery = query as any;

  // Add any validation or cleanup logic for deletions
  console.log('Class removal trigger called with query:', typedQuery);
  return typedQuery;
};
