// triggers/student.ts
import type { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>rigger, RemoveTrigger } from 'blade/types';

// During triggers - required by <PERSON> for write operations
export const during = {
  add: (...args: any[]) => {
    const [query, _multiple, _options] = args;
    const typedQuery = query as any;

    // Ensure query.with exists
    if (!typedQuery.with) {
      return typedQuery;
    }

    // Handle both single object and array cases
    const processStudentData = (studentData: any) => {
      console.log('Student during.add trigger - processing data:', studentData);

      // Set default values
      studentData.grade = studentData.grade || '';
      studentData.createdAt = studentData.createdAt || new Date();
      studentData.updatedAt = studentData.updatedAt || new Date();

      console.log('Student during.add trigger - processed data:', studentData);
      return studentData;
    };

    // Handle array of students
    if (Array.isArray(typedQuery.with)) {
      typedQuery.with = typedQuery.with.map(processStudentData);
    } else {
      // Handle single student
      typedQuery.with = processStudentData(typedQuery.with);
    }

    return typedQuery;
  },

  set: (...args: any[]) => {
    const [query, _multiple, _options] = args;
    const typedQuery = query as any;

    // Ensure query.to exists
    if (!typedQuery.to) {
      return typedQuery;
    }

    // Update timestamp
    typedQuery.to.updatedAt = new Date();

    console.log('Student during.set trigger - processed data:', typedQuery.to);
    return typedQuery;
  },

  remove: (...args: any[]) => {
    const [query, _multiple, _options] = args;
    const typedQuery = query as any;

    // Add any validation or cleanup logic for deletions
    console.log('Student during.remove trigger called with query:', typedQuery);
    return typedQuery;
  }
};

// Trigger for creating students
export const add: AddTrigger = (...args) => {
  const [query, _multiple, _options] = args;
  const typedQuery = query as any;

  // Ensure query.with exists
  if (!typedQuery.with) {
    return typedQuery;
  }

  // Handle both single object and array cases
  const processStudentData = (studentData: any) => {
    console.log('Student creation trigger - processing data:', studentData);

    // Set default values
    studentData.grade = studentData.grade || '';
    studentData.createdAt = studentData.createdAt || new Date();
    studentData.updatedAt = studentData.updatedAt || new Date();

    console.log('Student trigger - processed student data:', studentData);
    return studentData;
  };

  // Handle array of students
  if (Array.isArray(typedQuery.with)) {
    typedQuery.with = typedQuery.with.map(processStudentData);
  } else {
    // Handle single student
    typedQuery.with = processStudentData(typedQuery.with);
  }

  return typedQuery;
};

// Trigger for updating students
export const set: SetTrigger = (...args) => {
  const [query, _multiple, _options] = args;
  const typedQuery = query as any;

  // Ensure query.to exists
  if (!typedQuery.to) {
    return typedQuery;
  }

  // Update timestamp
  typedQuery.to.updatedAt = new Date();

  console.log('Student update trigger - processed data:', typedQuery.to);
  return typedQuery;
};

// Trigger for getting students (can be used for access control)
export const get: GetTrigger = (...args) => {
  const [query, _multiple, _options] = args;
  const typedQuery = query as any;

  // Add any access control logic here if needed
  return typedQuery;
};

// Trigger for removing students
export const remove: RemoveTrigger = (...args) => {
  const [query, _multiple, _options] = args;
  const typedQuery = query as any;

  // Add any validation or cleanup logic for deletions
  console.log('Student removal trigger called with query:', typedQuery);
  return typedQuery;
};

// Trigger to run after student creation (synchronous)
export const afterAdd = async (query: any, _multiple: any, _options: any) => {
  const studentData = query.with;

  if (!studentData) {
    return [];
  }

  console.log('👨‍🎓 Student afterAdd trigger called for:', studentData.userId);

  // Here you could add logic to:
  // - Send welcome notifications
  // - Create default assignments
  // - Update related records

  return [];
};
