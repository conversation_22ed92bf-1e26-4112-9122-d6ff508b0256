// triggers/invitation.ts
import type { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>rigger, RemoveTrigger } from 'blade/types';

// During triggers - required by Blade for write operations
export const during = {
  add: (...args: any[]) => {
    const [query, _multiple, _options] = args;
    const typedQuery = query as any;

    // Ensure query.with exists
    if (!typedQuery.with) {
      return typedQuery;
    }

    // Handle both single object and array cases
    const processInvitationData = (invitationData: any) => {
      console.log('Invitation during.add trigger - processing data:', invitationData);

      // Set default values
      invitationData.status = invitationData.status || 'pending';
      invitationData.createdAt = invitationData.createdAt || new Date();
      invitationData.updatedAt = invitationData.updatedAt || new Date();

      // Set default expiration if not provided (7 days from now)
      if (!invitationData.expiresAt) {
        const expiresAt = new Date();
        expiresAt.setDate(expiresAt.getDate() + 7);
        invitationData.expiresAt = expiresAt;
      }

      console.log('Invitation during.add trigger - processed data:', invitationData);
      return invitationData;
    };

    // Handle array of invitations
    if (Array.isArray(typedQuery.with)) {
      typedQuery.with = typedQuery.with.map(processInvitationData);
    } else {
      // Handle single invitation
      typedQuery.with = processInvitationData(typedQuery.with);
    }

    return typedQuery;
  },

  set: (...args: any[]) => {
    const [query, _multiple, _options] = args;
    const typedQuery = query as any;

    // Ensure query.to exists
    if (!typedQuery.to) {
      return typedQuery;
    }

    // Update timestamp
    typedQuery.to.updatedAt = new Date();

    console.log('Invitation during.set trigger - processed data:', typedQuery.to);
    return typedQuery;
  },

  remove: (...args: any[]) => {
    const [query, _multiple, _options] = args;
    const typedQuery = query as any;

    // Add any validation or cleanup logic for deletions
    console.log('Invitation during.remove trigger called with query:', typedQuery);
    return typedQuery;
  }
};

// Trigger for creating invitations
export const add: AddTrigger = (...args) => {
  const [query, _multiple, _options] = args;
  const typedQuery = query as any;

  // Ensure query.with exists
  if (!typedQuery.with) {
    return typedQuery;
  }

  // Handle both single object and array cases
  const processInvitationData = (invitationData: any) => {
    console.log('Invitation creation trigger - processing data:', invitationData);

    // Set default values
    invitationData.status = invitationData.status || 'pending';
    invitationData.createdAt = invitationData.createdAt || new Date();
    invitationData.updatedAt = invitationData.updatedAt || new Date();

    // Set default expiration if not provided (7 days from now)
    if (!invitationData.expiresAt) {
      const expiresAt = new Date();
      expiresAt.setDate(expiresAt.getDate() + 7);
      invitationData.expiresAt = expiresAt;
    }

    console.log('Invitation trigger - processed invitation data:', invitationData);
    return invitationData;
  };

  // Handle array of invitations
  if (Array.isArray(typedQuery.with)) {
    typedQuery.with = typedQuery.with.map(processInvitationData);
  } else {
    // Handle single invitation
    typedQuery.with = processInvitationData(typedQuery.with);
  }

  return typedQuery;
};

// Trigger for updating invitations
export const set: SetTrigger = (...args) => {
  const [query, _multiple, _options] = args;
  const typedQuery = query as any;

  // Ensure query.to exists
  if (!typedQuery.to) {
    return typedQuery;
  }

  // Update timestamp
  typedQuery.to.updatedAt = new Date();

  console.log('Invitation update trigger - processed data:', typedQuery.to);
  return typedQuery;
};

// Trigger for getting invitations (can be used for access control)
export const get: GetTrigger = (...args) => {
  const [query, _multiple, _options] = args;
  const typedQuery = query as any;

  // Add any access control logic here if needed
  return typedQuery;
};

// Trigger for removing invitations
export const remove: RemoveTrigger = (...args) => {
  const [query, _multiple, _options] = args;
  const typedQuery = query as any;

  // Add any validation or cleanup logic for deletions
  console.log('Invitation removal trigger called with query:', typedQuery);
  return typedQuery;
};
