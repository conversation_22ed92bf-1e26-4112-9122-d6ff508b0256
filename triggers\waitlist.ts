// triggers/waitlist.ts
import type { <PERSON>d<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, GetTrigger, RemoveTrigger } from 'blade/types';

// During triggers - required by Blade for write operations
export const during = {
  add: (...args: any[]) => {
    const [query, _multiple, _options] = args;
    const typedQuery = query as any;

    // Ensure query.with exists
    if (!typedQuery.with) {
      return typedQuery;
    }

    // Handle both single object and array cases
    const processWaitlistData = (waitlistData: any) => {
      console.log('Waitlist during.add trigger - processing data:', waitlistData);

      // Set default values
      waitlistData.isApproved = waitlistData.isApproved || false;
      waitlistData.createdAt = waitlistData.createdAt || new Date();

      console.log('Waitlist during.add trigger - processed data:', waitlistData);
      return waitlistData;
    };

    // Handle array of waitlist entries
    if (Array.isArray(typedQuery.with)) {
      typedQuery.with = typedQuery.with.map(processWaitlistData);
    } else {
      // Handle single waitlist entry
      typedQuery.with = processWaitlistData(typedQuery.with);
    }

    return typedQuery;
  },

  set: (...args: any[]) => {
    const [query, _multiple, _options] = args;
    const typedQuery = query as any;

    // Ensure query.to exists
    if (!typedQuery.to) {
      return typedQuery;
    }

    // Set approval timestamp if being approved
    if (typedQuery.to.isApproved === true && !typedQuery.to.approvedAt) {
      typedQuery.to.approvedAt = new Date();
    }

    console.log('Waitlist during.set trigger - processed data:', typedQuery.to);
    return typedQuery;
  },

  remove: (...args: any[]) => {
    const [query, _multiple, _options] = args;
    const typedQuery = query as any;

    // Add any validation or cleanup logic for deletions
    console.log('Waitlist during.remove trigger called with query:', typedQuery);
    return typedQuery;
  }
};

// Trigger for creating waitlist entries
export const add: AddTrigger = (...args) => {
  const [query, _multiple, _options] = args;
  const typedQuery = query as any;

  // Ensure query.with exists
  if (!typedQuery.with) {
    return typedQuery;
  }

  // Handle both single object and array cases
  const processWaitlistData = (waitlistData: any) => {
    console.log('Waitlist creation trigger - processing data:', waitlistData);

    // Set default values
    waitlistData.isApproved = waitlistData.isApproved || false;
    waitlistData.createdAt = waitlistData.createdAt || new Date();

    console.log('Waitlist trigger - processed waitlist data:', waitlistData);
    return waitlistData;
  };

  // Handle array of waitlist entries
  if (Array.isArray(typedQuery.with)) {
    typedQuery.with = typedQuery.with.map(processWaitlistData);
  } else {
    // Handle single waitlist entry
    typedQuery.with = processWaitlistData(typedQuery.with);
  }

  return typedQuery;
};

// Trigger for updating waitlist entries
export const set: SetTrigger = (...args) => {
  const [query, _multiple, _options] = args;
  const typedQuery = query as any;

  // Ensure query.to exists
  if (!typedQuery.to) {
    return typedQuery;
  }

  // Set approval timestamp if being approved
  if (typedQuery.to.isApproved === true && !typedQuery.to.approvedAt) {
    typedQuery.to.approvedAt = new Date();
  }

  console.log('Waitlist update trigger - processed data:', typedQuery.to);
  return typedQuery;
};

// Trigger for getting waitlist entries (can be used for access control)
export const get: GetTrigger = (...args) => {
  const [query, _multiple, _options] = args;
  const typedQuery = query as any;

  // Add any access control logic here if needed
  return typedQuery;
};

// Trigger for removing waitlist entries
export const remove: RemoveTrigger = (...args) => {
  const [query, _multiple, _options] = args;
  const typedQuery = query as any;

  // Add any validation or cleanup logic for deletions
  console.log('Waitlist removal trigger called with query:', typedQuery);
  return typedQuery;
};
