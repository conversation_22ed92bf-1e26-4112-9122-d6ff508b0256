// triggers/teacherSubject.ts
import type { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, RemoveTrigger } from 'blade/types';

// During triggers - required by <PERSON> for write operations
export const during = {
  add: (...args: any[]) => {
    const [query, _multiple, _options] = args;
    const typedQuery = query as any;

    // Ensure query.with exists
    if (!typedQuery.with) {
      return typedQuery;
    }

    // Handle both single object and array cases
    const processTeacherSubjectData = (relationshipData: any) => {
      console.log('TeacherSubject during.add trigger - processing data:', relationshipData);

      // Set default values
      relationshipData.createdAt = relationshipData.createdAt || new Date();

      console.log('TeacherSubject during.add trigger - processed data:', relationshipData);
      return relationshipData;
    };

    // Handle array of relationships
    if (Array.isArray(typedQuery.with)) {
      typedQuery.with = typedQuery.with.map(processTeacherSubjectData);
    } else {
      // Handle single relationship
      typedQuery.with = processTeacherSubjectData(typedQuery.with);
    }

    return typedQuery;
  },

  set: (...args: any[]) => {
    const [query, _multiple, _options] = args;
    const typedQuery = query as any;

    // Ensure query.to exists
    if (!typedQuery.to) {
      return typedQuery;
    }

    console.log('TeacherSubject during.set trigger - processed data:', typedQuery.to);
    return typedQuery;
  },

  remove: (...args: any[]) => {
    const [query, _multiple, _options] = args;
    const typedQuery = query as any;

    // Add any validation or cleanup logic for deletions
    console.log('TeacherSubject during.remove trigger called with query:', typedQuery);
    return typedQuery;
  }
};

// Trigger for creating teacher-subject relationships
export const add: AddTrigger = (...args) => {
  const [query, _multiple, _options] = args;
  const typedQuery = query as any;

  // Ensure query.with exists
  if (!typedQuery.with) {
    return typedQuery;
  }

  // Handle both single object and array cases
  const processTeacherSubjectData = (relationshipData: any) => {
    console.log('TeacherSubject creation trigger - processing data:', relationshipData);

    // Set default values
    relationshipData.createdAt = relationshipData.createdAt || new Date();

    console.log('TeacherSubject trigger - processed relationship data:', relationshipData);
    return relationshipData;
  };

  // Handle array of relationships
  if (Array.isArray(typedQuery.with)) {
    typedQuery.with = typedQuery.with.map(processTeacherSubjectData);
  } else {
    // Handle single relationship
    typedQuery.with = processTeacherSubjectData(typedQuery.with);
  }

  return typedQuery;
};

// Trigger for updating teacher-subject relationships
export const set: SetTrigger = (...args) => {
  const [query, _multiple, _options] = args;
  const typedQuery = query as any;

  // Ensure query.to exists
  if (!typedQuery.to) {
    return typedQuery;
  }

  console.log('TeacherSubject update trigger - processed data:', typedQuery.to);
  return typedQuery;
};

// Trigger for getting teacher-subject relationships (can be used for access control)
export const get: GetTrigger = (...args) => {
  const [query, _multiple, _options] = args;
  const typedQuery = query as any;

  // Add any access control logic here if needed
  return typedQuery;
};

// Trigger for removing teacher-subject relationships
export const remove: RemoveTrigger = (...args) => {
  const [query, _multiple, _options] = args;
  const typedQuery = query as any;

  // Add any validation or cleanup logic for deletions
  console.log('TeacherSubject removal trigger called with query:', typedQuery);
  return typedQuery;
};
