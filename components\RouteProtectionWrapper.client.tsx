// components/RouteProtectionWrapper.client.tsx
'use client';

import { useRouteProtection, routeConfig } from '../utils/route-protection';
import { useLocation, usePopulatePathname } from 'blade/hooks';

interface RouteProtectionWrapperProps {
  children: React.ReactNode;
}

export const RouteProtectionWrapper: React.FC<RouteProtectionWrapperProps> = ({ children }) => {
  const { user, loading } = useRouteProtection();
  const location = useLocation();
  const populatePathname = usePopulatePathname();

  // Find the current route configuration using resolved pathname
  const currentPath = populatePathname(location.pathname);
  const matchedRoute = routeConfig
    .sort((a, b) => b.path.length - a.path.length)
    .find(route => currentPath.startsWith(route.path));

  // Enhanced logging for debugging
  console.log('🔍 RouteProtectionWrapper check:', {
    rawPath: location.pathname,
    currentPath,
    user: user ? { id: user.id, role: user.role, slug: user.slug } : null,
    loading,
    matchedRoute
  });

  // If still loading auth state, don't render anything to prevent flash
  if (loading) {
    console.log('🔍 RouteProtectionWrapper - still loading, not rendering');
    return null;
  }

  // Check if we're in a recent authentication transition
  const isRecentlyAuthenticated = () => {
    const lastAuthTime = sessionStorage.getItem('lastAuthTime');
    if (!lastAuthTime) return false;
    const timeDiff = Date.now() - parseInt(lastAuthTime);
    return timeDiff < 15000; // 15 second grace period
  };

  // During recent authentication, be more permissive to avoid flash
  if (isRecentlyAuthenticated()) {
    console.log('🔍 RouteProtectionWrapper - recently authenticated, allowing render');
    return <>{children}</>;
  }

  // For public routes that should redirect authenticated users (/ and /login)
  if (matchedRoute?.type === 'public' && user && (currentPath === '/' || currentPath === '/login')) {
    // Don't render public content for authenticated users - they'll be redirected
    console.log('🔍 RouteProtectionWrapper - authenticated user on public route, not rendering');
    return null;
  }

  // For protected routes without authentication
  if (matchedRoute?.type === 'protected' && !user) {
    // Don't render protected content for unauthenticated users - they'll be redirected
    console.log('🔍 RouteProtectionWrapper - unauthenticated user on protected route, not rendering');
    return null;
  }

  // For protected routes with wrong role
  if (matchedRoute?.type === 'protected' && user && matchedRoute.allowedRoles && !matchedRoute.allowedRoles.includes(user.role)) {
    // Don't render content for users with wrong role - they'll be redirected
    console.log('🔍 RouteProtectionWrapper - wrong role for protected route, not rendering');
    return null;
  }

  // Safe to render content
  console.log('🔍 RouteProtectionWrapper - safe to render content');
  return <>{children}</>;
};